<script src='https://cdn.tiny.cloud/1/0x8e99nvlpzhz0k7spk5ktl338qoqbhlisbuv13y7n4k8sst/tinymce/5/tinymce.min.js' referrerpolicy="origin">
</script>
<script>
    tinymce.init({
        selector: '#infotext',
        plugins: 'advlist autolink lists link image charmap print preview hr anchor pagebreak',
        toolbar_mode: 'floating',
        menubar: 'file edit view insert format tools table tc help',
        toolbar: 'undo redo | bold italic underline strikethrough | fontselect fontsizeselect formatselect | alignleft aligncenter alignright alignjustify | outdent indent |  numlist bullist checklist | forecolor backcolor casechange permanentpen formatpainter removeformat | pagebreak | charmap emoticons | fullscreen  preview save print | insertfile image media pageembed template link anchor codesample | a11ycheck ltr rtl | showcomments addcomment',
    });
</script>

<h1>Инструкция по закупке <%= @purchase.name %></h1>

<%= form_for :purchase, :url => {:action => :'save_info'}, :html => {:class => 'form-horizontal', :multipart => true} do |f| %>
  <div class="form-group row">
    <div class="offset-lg-0 col-lg-12">
    <textarea id="infotext" name="info">
      <%=@purchase.info %>
    </textarea>
</div>
</div>
  <div class="form-group row">
    <div class="offset-lg-0 col-lg-12">
  <%= f.submit 'Сохранить', class: 'btn btn-primary' %>
    </div>
  </div>
<% end %>