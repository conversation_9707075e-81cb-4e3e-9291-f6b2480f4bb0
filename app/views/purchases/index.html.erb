<!-- Latest compiled and minified CSS -->
<link rel="stylesheet" href="https://unpkg.com/bootstrap-table@1.18.1/dist/bootstrap-table.min.css">

<!-- Latest compiled and minified JavaScript -->
<script src="https://unpkg.com/bootstrap-table@1.18.1/dist/bootstrap-table.min.js"></script>
<!-- Latest compiled and minified Locales -->
<script src="https://unpkg.com/bootstrap-table@1.18.1/dist/locale/bootstrap-table-ru-RU.min.js"></script>

<h1>Мои покупки</h1>

<table class="table table-striped" data-toggle="table" data-search="true" data-sort-name="date" data-sort-order="desc">
  <thead>
  <tr>
    <th data-sortable="true">Название</th>
    <th data-sortable="true" data-field="date">Последнее действие</th>
    <th data-sortable="true">Статус</th>
    <th data-sortable="true">Разработчик</th>
  </tr>
  </thead>

  <tbody>
  <% @purchases.each do |purchase| %>
      <tr>
        <td><span style="display: none"><%=purchase.name %></span><%= link_to purchase.name, purchase_path(purchase) %></td>
        <td><%= purchase.updated_at %></td>
        <td><%= purchase.status %> <%= purchase.dev_state %></td>
        <td><%= purchase.developer&.email %></td>
      </tr>
  <% end %>
  </tbody>
</table>
