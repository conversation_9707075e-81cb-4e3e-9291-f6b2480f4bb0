# frozen_string_literal: true

class SpParseProducts
  def search_sku(sku)
    page = @api.agent.get "https://www.100sp.ru/search?query=#{sku}&type=good&sort=1"
    good_link = page.at('a[data-item-id]')&.attr('href')
    if good_link
      page = @api.agent.get good_link
      page_sku = page.at('span[data-field="articul"]').text.strip

      price = nil
      if page_sku == sku
        if page.at('.good-price-wrapper p.new-price')
          price = page.at('.good-price-wrapper p.new-price').text.to_i
        else
          price = page.at('.good-price-wrapper p').text.to_i
        end
      end
    end

    price
  end
  def run
    @api = SpApi.new
    search_sku('8809560220116')
  end

end
