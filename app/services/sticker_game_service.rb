# frozen_string_literal: true

class StickerGameService
  def save_pic(gid, url)
    @picpath = Spup2::Application.config.image_path + 'image_game/'
    FileUtils.mkpath(@picpath) unless File.exist? @picpath
    fname = "#{@picpath}#{gid}.jpg"
    @api.agent.download(url, fname)
    fname
  end

  def delete_pic(gid, pic_id)
    @api.agent.post('https://www.100sp.ru/ajaxUploader/deletePicture/' + gid, { :picid => pic_id, :connected_id => gid, :connected_type => 'good' }, @api.ajax_headers)
  end
  def upload_pic(gid, fname)
    data = { 'id' => gid, 'Filedata' => File.open(fname, 'rb') }
    r = @api.agent.post("https://www.100sp.ru/org/good/uploadPicture/#{gid}", data, @api.ajax_headers)
    puts r.body
  end

  def add_sticker(img, sticker = '/home/<USER>/smile.png')
    image = Magick::Image.read(img).first

    # Load the sticker image (with alpha channel)
    sticker = Magick::Image.read(sticker).first

    # Get dimensions for positioning the sticker in the lower-right corner
    image_width = image.columns
    image_height = image.rows
    sticker_width = sticker.columns
    sticker_height = sticker.rows

    # Calculate maximum allowable sticker size (25% of main image width and height)
    max_width = image_width * 0.5
    max_height = image_height * 0.5

    # Scale down the sticker if necessary
    if sticker_width > max_width || sticker_height > max_height
      scale_factor = [max_width / sticker_width, max_height / sticker_height].min
      sticker = sticker.resize(scale_factor)
      sticker_width = sticker.columns
      sticker_height = sticker.rows
    end

    # Calculate the position for the sticker (lower-right corner)
    x_position = image_width - sticker_width - 10
    y_position = image_height - sticker_height - 10

    # Composite the sticker onto the main image at the calculated position
    result = image.composite(sticker, x_position, y_position, Magick::OverCompositeOp)

    # Generate the filename for the output

    output_filename = Rails.root.join('files', "output_#{File.basename(img, ".*")}_with_sticker.png")

    # Save the result to a file
    result.write(output_filename)

    # Return the output filename
    output_filename
  end

  def restore_all_pics
    @api = SpApi.new
    ProductGameInfo.where(gid: gid, sticker_remove_time: nil).each do |info|
      upload_pic(info.gid, info.old_image)
      info.update(sticker_remove_time: DateTime.now)
    end
  end
  def add_sticker_to_product(product)
    original_pic = save_pic(product[:product][:gid], product[:product][:pics][0])
    pic_id = product[:product][:pics][0].split('/').last
    sticker = nil
    sticker = 'C:/Users/<USER>/Downloads/smile.png' if Rails.env.development?
    pic_with_sticker = add_sticker(original_pic, sticker)
    delete_pic(product[:product][:gid], pic_id)
    upload_pic(product[:product][:gid], pic_with_sticker)
    ProductGameInfo.create(gid: product[:product][:gid], cid: product[:cid], pid: product[:pid], old_image: original_pic, sticker_add_time: Time.now)
  end

  def add_sticker_to_products(products)
    products.each do |pr|
      add_sticker_to_product(pr)
    end
  end


  def add_new_stickers
    products = get_products_to_add_stickers
    add_sticker_to_products(products)
  end

  def get_products_to_add_stickers
    @api = SpApi.new
    collections = []

    if Rails.env.development?
      collections << { cid: 10451243, name: 'Воск для депиляции', pid: 782280 }
    else
      purchases = SpPurchase.where(state: ['Активна', 'Сбор заказов', 'Дозаказ']).where('close_time > ?', 2.days.from_now).sample(10)
      purchases.each do |purchase|
        cols = @api.load_sp_collections(purchase.pid)
        cid = cols.keys.sample
        collections << { cid: cid, name: cols[cid], pid: purchase.pid }
      end
    end

    products = []
    collections.each do |col|
      ps = @api.process_src_col(col[:pid], col[:cid])[:goods]
      ps = ps.delete_if { |p| p[:pics].nil? || p[:pics].empty? }
      pr = ps.sample
      pr = ps[0] if Rails.env.development?

      products << { product: pr, cid: col[:cid], pid: col[:pid] }
    end
    products
  end

  def run

  end
end
