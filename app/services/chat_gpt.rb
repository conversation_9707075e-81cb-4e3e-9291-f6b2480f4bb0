# frozen_string_literal: true

class ChatGpt
  attr_reader :client

  def initialize
    @client = OpenAI::Client.new(access_token: "***************************************************", log_errors: true)
  end

  def get_response_functions(data)
    res = {}
    if data['tool_calls']
      data['tool_calls'].each do |tool_call|
        if tool_call['type'] == 'function'
          res[tool_call['function']['name']] = JSON.parse(tool_call['function']['arguments'])
        end
      end
    end

    res
  end

  def chat_gpt_functions_call(prompt, functions, model: 'gpt-3.5-turbo', image_url: nil, temperature: 0.2, batch_id: nil)
    tries ||= 3
    params = {
      model: model, # Required.
      messages: [{ role: "user", content: [
        { "type": "text", "text": prompt }
      ]
                 }], # Required.
      temperature: temperature,
      tool_choice: 'auto'
    }
    params[:tools] = functions.map { |f| { "type": "function", "function": f } }

    if image_url
      params[:messages][0][:content] << {

        "type": "image_url",
        "image_url": {
          "url": image_url
        }
      }

    end

    if functions.count == 1
      params[:tool_choice] = { "type": "function", "function": { "name": functions[0][:name] } }
    end

    if batch_id
      return { "custom_id" => batch_id, 'method' => 'POST', "url" => "/v1/chat/completions", 'body' => params }.to_json
    end

    res = @client.chat(parameters: params)
    if res['usage']
      price = res['usage']['total_tokens'] / 1000000.0 * 5 + res['usage']['completion_tokens'] / 1000000.0 * 15
      puts "Prompt tokens: #{res['usage']['prompt_tokens']}, completion tokens: #{res['usage']['completion_tokens']}, total price: #{price} usd, #{price * 90} rub"
    end
    d = res.dig("choices", 0, "message")
    raise if d.nil?

    get_response_functions(d)

  rescue Exception => e
    puts e
    tries -= 1
    if tries > 0
      sleep(2)
      retry
    end
  end

  def request(prompt, model: 'gpt-4o-mini', pics: nil, temperature: 0.2, max_tokens: nil, schema: nil, batch_id: nil)
    tries ||= 3
    params = {
      model: model, # Required.
      messages: [{ role: "user", content: prompt }], # Required.
      temperature: temperature,
    }

    params[:max_tokens] = max_tokens if max_tokens
    # params[:messages].unshift({role: 'system', content: system_prompt}) if system_prompt

    if pics
      params[:messages][-1][:content] = [{ type: 'text', text: params[:messages][0][:content] }]
      params[:messages][-1][:content] += pics.map { |p| { type: 'image_url', image_url: { url: p } } }
    end

    if schema
      params[:response_format] = {
        type: "json_schema",
        json_schema: schema
      }
    end

    if batch_id
      return { "custom_id" => batch_id, 'method' => 'POST', "url" => "/v1/chat/completions", 'body' => params }.to_json
    end

    puts params

    res = @client.chat(parameters: params)
    res.dig("choices", 0, "message", "content")

  rescue Exception => e
    puts e
    tries -= 1
    if tries > 0
      sleep(2)
      retry
    end
  end

  def create_new_description(name, description, image_url, batch_id: nil, model: 'gpt-4.1', prompt: nil)
    if prompt.nil?
      if image_url
        prompt = "Ты современный маркетолог интернет-магазина, создай на основе изображения, описания новое описание, стимулирующее покупку, обязательно сохранив всю важную информацию. Не используй HTML-тэги. Напиши текст по формуле AIDA, но не выделяя блоки явно, а красиво оформи все в связный текст. Включи четкий и мотивирующий призыв к действию в конце описания. Включи эмоциональные триггеры, связанные с использованием товара. Не упоминай артикул или код товара/модели. Не будь излишне многословен. Разбей на абзацы. Товар :'#{name.strip}', описание: #{description}"
      else
        prompt = "Ты современный маркетолог интернет-магазина, создай на основе описания новое описание, стимулирующее покупку, обязательно сохранив всю важную информацию. Не используй HTML-тэги. Напиши текст по формуле AIDA, но не выделяя блоки явно, а красиво оформи все в связный текст. Включи четкий и мотивирующий призыв к действию в конце описания. Включи эмоциональные триггеры, связанные с использованием товара. Не упоминай артикул или код товара/модели. Не будь излишне многословен. Разбей на абзацы. Товар :'#{name.strip}', описание: #{description}"
      end
    end

    schema = {
      name: 'descr_schema',
      strict: true,
      schema: {
        type: 'object',
        properties: {
          description: { type: 'string', description: 'Описание товара' }
        },
        required: ['description'],
        additionalProperties: false
      }
    }
    pics = nil
    pics = [image_url] if image_url
    res = request(prompt, model: model, pics: pics, temperature: 0.5, max_tokens: 1000, schema: schema, batch_id: batch_id)
    return res if batch_id
    JSON.parse(res)['description']

  end

  def create_new_name(name, prompt:nil, image_url: nil, batch_id: nil)
    # r = chat_gpt_functions_call("Ты современный маркетолог интернет-магазина, создай на основе изображения, описания новое описание, стимулирующее покупку, сохранив всю информацию Не используй HTML-тэги. Товар :'#{name}', описание: #{description}", [
    prompt = 'Улучши название товара для интернет-магазина, не нужно лишнего креатива:' if prompt.nil?
    prompt = "#{prompt} #{name.strip}"

    schema = {
      name: 'name_schema',
      strict: true,
      schema: {
        type: 'object',
        properties: {
          name: { type: 'string', description: 'Название товара' }
        },
        required: ['name'],
        additionalProperties: false
      }
    }
    pics = nil
    pics = [image_url] if image_url
    res = request(prompt, model: 'gpt-4o-mini', pics: pics, temperature: 0.5, max_tokens: 100, schema: schema, batch_id: batch_id)
    return res if batch_id
    JSON.parse(res)['name']
  end

  def get_product_dimensions(name, description: nil, batch_id: nil)
    # r = chat_gpt_functions_call("Ты современный маркетолог интернет-магазина, создай на основе изображения, описания новое описание, стимулирующее покупку, сохранив всю информацию Не используй HTML-тэги. Товар :'#{name}', описание: #{description}", [
    if description
      prompt = "Сообщи размеры и вес товара с упаковкой:  #{name.strip}, описание: #{description}"
    else
      prompt = "Сообщи размеры и вес товара с упаковкой: #{name.strip}"
    end

    schema = {
      name: 'dim_schema',
      schema: {
        strict: true,
        type: 'object',
        properties: {
          weight: { type: 'integer', description: 'Вес упаковки в граммах' },
          width: { type: 'integer', description: 'Ширина упаковки в миллиметрах' },
          depth: { type: 'integer', description: 'Глубина упаковки в миллиметрах' },
          height: { type: 'integer', description: 'Высота упаковки в миллиметрах' },
        },
        required: ['weight', 'width', 'depth', 'height'],
        additionalProperties: false
      }
    }
    res = request(prompt, model: 'gpt-4.1', pics: nil, temperature: 0.1, max_tokens: 200, schema: schema, batch_id: batch_id)
    return res if batch_id
    JSON.parse(res)
  end

  def get_product_weight(name, batch_id: nil)
    schema = {
      name: 'dim_schema',
      strict: true,
      schema: {
        type: 'object',
        properties: {
          weight: { type: 'integer', description: 'Вес упаковки в граммах' },
        },
        required: ['weight'],
        additionalProperties: false
      }
    }
    res = request("Определи вес товара с упаковкой: #{name}", model: 'gpt-4.1', temperature: 0.1, max_tokens: 200, schema: schema, batch_id: batch_id)
    return res if batch_id

    (JSON.parse(res)['weight']*1.1).ceil(-1)
  end

  def upload_file(fn, purpose)
    @client.files.upload(parameters: { file: fn, purpose: purpose })
  end

  def create_batch(file_id)
    @client.batches.create(
      parameters: {
        input_file_id: file_id,
        endpoint: "/v1/chat/completions",
        completion_window: "24h"
      }
    )
  end
end
