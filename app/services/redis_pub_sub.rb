class RedisPubSub
  attr_reader :redis

  CHANNEL_PREFIX = 'spup:'

  def initialize
    @redis = Redis.new
  end

  # Publishes a message to a given channel
  def publish(channel, message)
    redis.publish(CHANNEL_PREFIX + channel, message)
  end

  # Subscribes to a given channel
  def subscribe(channel, &block)
    redis.subscribe(CHANNEL_PREFIX + channel) do |on|
      on.message do |_channel, message|
        block.call(message)
      end
    end
  end

  # Unsubscribes from all the channels
  def unsubscribe
    redis.unsubscribe
  end
end
