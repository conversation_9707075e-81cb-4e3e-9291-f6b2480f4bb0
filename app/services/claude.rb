class Claude
  def initialize
    #@client = Anthropic::Client.new(access_token: "************************************************************************************************************")
    @client = Anthropic::Client.new(access_token: "************************************************************************************************************")
  end

  def get_response_functions(data)
    res = {}
    if data['tool_calls']
      data['tool_calls'].each do |tool_call|
        if tool_call['type'] == 'function'
          res[tool_call['function']['name']] = JSON.parse(tool_call['function']['arguments'])
        end
      end
    end

    res
  end

  def claude_functions_call(prompt, functions, model: 'claude-3-5-sonnet-20240620', image_url: nil, temperature: 0.2)
    tries ||= 3
    params = {
      model: model, # Required.
      messages: [{ role: "user", content: [{type: 'text', text: prompt }] }], # Required.
      temperature: temperature,
      max_tokens: 500,
      tool_choice: 'auto'
    }
    params[:tools] = functions if functions && functions[0]

    if image_url
      agent = Mechanize.new
      params[:messages][0][:content] << {
        "type": "image",
        "source": {
          "type": 'base64',
          'media_type': 'image/jpeg',
          'data': Base64.strict_encode64(agent.get(image_url).body)
        }
      }

    end

    if functions.count == 1
      params[:tool_choice] = { "type": "tool", "name": functions[0][:name] }
    end

    res = @client.messages(parameters: params)
    d = res.dig("choices", 0, "message")
    raise if d.nil?

    get_response_functions(d)

  rescue Exception => e
    puts e
    tries -= 1
    if tries > 0
      sleep(2)
      retry
    end
  end

  def request(prompt, pics:nil, model: 'claude-3-haiku-20240307', system_prompt: nil, temperature: 0, max_tokens: 1000)
    tries ||= 3
    params = {
      model: model, # Required.
      messages: [{ role: "user", content: [{type: 'text', text: prompt }] }], # Required.
      temperature: temperature,
      max_tokens: max_tokens
    }

    params['system'] = system_prompt if system_prompt

    if pics
      pics.each do |pic|
        img = Magick::Image::read(pic.path).first
        img.resize_to_fit!(256,256)
        jpeg_blob = img.to_blob  { |img| img.format = 'JPEG' }

        base64_encoded = Base64.strict_encode64(jpeg_blob)

        params[:messages][0][:content] << {
          "type": "image",
          "source": {
            "type": 'base64',
            'media_type': 'image/jpeg',
            'data': base64_encoded
          }
        }
        end
    end

    res = @client.messages(parameters: params)
    res.dig("content", 0, "text")

  rescue Exception => e
    tries -= 1
    if tries > 0
      sleep(2)
      retry
    end
  end

  def create_new_description(name, description, image_url)
    r = claude_functions_call("Ты совеременный маркетолог интернет-магазина, создай на основе изображения, описания новое описание, стимулирующее покупку, сохранив всю информацию. Не используй эмодзи, будь не сликшом многословен. Пиши красиво, но чтобы читалось быстро и легко: Товар :'#{name}', описание: #{description}", [
      {
        name: 'save_description',
        "description": "Saves product description",
        "input_schema": {
          "type": "object",
          "properties": {
            "description": { "type": "string", "description": "Description of product" }
          }
        },
        #"required": ["description"]chat_gpt_functions_call
      }
    ],image_url: image_url, temperature: 0.5) #

    r['save_description']['description'] if r && r['save_description']
  end

  def get_product_weight(name)
    r = chat_gpt_functions_call("Provide weight for product: #{name}", [
      {
        name: 'save_weight',
        "description": "Saves product weight",
        "parameters": {
          "type": "object",
          "properties": {
            "weight": { "type": "number", "description": "Weight of product in grams" } #,
            #"reason": { "type": "string", "description": "Reason how weight has been calculated" }
          }
        },
        #"required": ["weight"]
      }
    ], model: 'gpt-4o-2024-11-20') #

    (r['save_weight']['weight'].to_f * 1.1).ceil if r && r['save_weight']
  end
end
