# frozen_string_literal: true

class SpLoadProducts
  def load_purchase(pid)
    @agent = Mechanize.new

    res = @agent.get 'https://www.100sp.ru/org/default/apiExportFullReport', { purchase: pid, dateFrom: '2010-01-01', dateTo: '2040-01-01' }, nil, { 'x-api-key' => SpApi::API_KEY }
    data = JSON.parse(res.body)
    if data['result']
      data['data'].each do |line|
        SpPurchaseData.where(oid: line['oid']).first_or_create.update(
          sp_purchase_id: pid,
          art: line['articul'],
          user_name: line['user_name'],
          gid: line['gid'],
          name: line['name'],
          purchase_name: line['purchase'],
          collection_name: line['collection'],
          status: line['status'],
          user_order_comment: line['order_comment'],
          org_order_comment: line['order_org_comment'],
          org_user_comment: line['user_org_comment'],
          org_megaorder_comment: line['megaorder_org_comment'],
          is_finished: line['is_finished'],
          finished: line['finished'],
          distributor_name: line['distributor_name'],
          distributor_id: line['distributor_id'],
        megaorder_id: line['megaorder_id']
        )
      end
    end

  end

  def run
    SpPurchase.where("state='Подготовка к раздаче' or (state='Ожидание товаров' and extract(day from now()-updated_at)>14)").each do |sp_purchase|
      load_purchase(sp_purchase.pid)
    end

    SpPurchase.where("state<>'Подготовка к раздаче' and state<>'Ожидание товаров'").each do |p|
      SpPurchaseData.where(sp_purchase_id: p.id).delete_all
    end
  end
end
