class TrackPurchases
  include SpAuthorize

  def notify(notify_data)
    puts notify_data.join("\n")
    Pony.mail({
                :to => ['<EMAIL>', 'lorab<PERSON><PERSON>@gmail.com'], #
                :via => :smtp,
                :via_options => {
                  :address => 'smtp.gmail.com',
                  :port                 => '587',
                  :enable_starttls_auto => true,
                  :user_name => '<EMAIL>',
                  :password => 'wkrrgjviuxkwdmhp',
                  :authentication => :plain, # :plain, :login, :cram_md5, no auth by default
                  :domain => 'localhost.localdomain' # the HELO domain provided by the client to the server
                },
                :from => '<EMAIL>', :subject => 'СБОР ДЕНЕГ', :html_body => notify_data.join("\n")
              })
  end

  def set_cookies(driver)
    agent = Mechanize.new
    p=agent.get('http://spup.primavon.ru/api/getsess')

    if p.body!='no data'
      (phpsessid,auth,au)=p.body.split ';'
      driver.manage.add_cookie(name: "PHPSESSID", value: phpsessid)
      driver.manage.add_cookie(name: "au", value: au)
    end
  end


  def save_pic(n)
    ts=Time.now.to_i
    fn="/home/<USER>/snapshots/images/#{n}_#{ts}.png"
    @driver.save_screenshot fn
  end

  def take_snapshot(pid)
    return if @snapshot_taken
    @snapshot_taken=true

    service = Selenium::WebDriver::Service.chrome(path: '/home/<USER>/chromedriver')
    options = Selenium::WebDriver::Chrome::Options.new
    options.add_argument('--headless')

    @driver = Selenium::WebDriver.for :chrome, options: options, service: service
    @driver.manage.window.resize_to(1920, 2000)

    @driver.get 'https://www.100sp.ru/vladivostok'
    set_cookies(@driver)
    @driver.get 'https://www.100sp.ru/vladivostok'
    save_pic("homepage_#{pid}")

    #@driver.execute_script("window.scrollTo(0,600)")

    @driver.get 'https://www.100sp.ru/org/purchase/index'
    save_pic("account1_#{pid}")

    @driver.get 'https://www.100sp.ru/org/purchase/index#status_2'
    save_pic("account2_#{pid}")

    @driver.get 'https://www.100sp.ru/org/purchase/index#status_15'
    save_pic("account3_#{pid}")
  end
  def run
    authorize
    page = @agent.get('https://www.100sp.ru/org/purchase/index')

    found_pid = []
    notify_data=[]

      page.search('table.purchases-table tr.purchases-table__row').each do |tr|
      next if tr.search('>td').count != 7
      purch_name = tr.search('td a')[1].text
      purch_href = tr.search('td a')[0].attr('href')
      purch_id = 0
      if /(\d+$)/ =~ purch_href
        purch_id = $1.to_s
      end

      found_pid << purch_id

      state = tr.search('td')[3].text.to_s
      mid = tr.search('.purchase-info-short a')[0].attr('href').to_s.split('/').last.to_i
      puts purch_name, purch_href, state

      publish_time = tr.at('span[data-datetime]').attr('data-datetime')
      d = DateTime.parse(publish_time)
      puts DateTime.now.to_i - d.to_i
      #if DateTime.now.to_i - d.to_i > 180 && DateTime.now.to_i - d.to_i < 600
        #take_snapshot(purch_id)
        #system("/bin/bash -l -c 'cd /home/<USER>/www/spup/spup && PATH=\"$HOME/.rbenv/shims\":\"$HOME/.rbenv/bin\":\"$PATH\" RUBY_YJIT_ENABLE=1 cd /home/<USER>/snapshots && ruby snapshot.rb #{purch_id}'")
      #end

      close_time = nil
      if state!='Активна'
        close_time = tr.at("div:contains('Сбор заказов до ') > span")&.attr('data-datetime')
        close_time = DateTime.parse(close_time) if close_time
      end

      delivery_days = tr.at('div.purchase-badge-infinity span.content')&.text.to_i

      purchase = SpPurchase.find_by_pid(purch_id)
      if purchase
        purchase.name = purch_name

        if state == 'Сбор денег' and purchase.state != 'Сбор денег'
          notify_data << "Покупка <a href='http://www.100sp.ru/purchase/#{purch_id}'>#{purch_name}</a> перешла в сбор денег<br/>"
        end

        if state == 'Сбор заказов' and purchase.state == 'Не начато'
          pubsub = RedisPubSub.new
          pubsub.publish('new_purchase', {type: 'purchase', purchase: purchase.as_json, action: 'start'}.to_json)
        end

        purchase.state=state
        purchase.close_time = close_time
        purchase.delivery_days=delivery_days
        purchase.save
      else
        purchase = SpPurchase.create(pid: purch_id, name: purch_name, state: state, mid: mid, user_id: 1, company_id: 1, delivery_days: delivery_days, close_time: close_time)
      end

    end

    SpPurchase.where(state: ['Сбор заказов','Дозаказ']).where.not(pid: found_pid).where('created_at < ?', 5.day.ago).each do |p|
      p.state = 'Не состоялась'
      p.save
    end

    SpPurchase.where(state: ['Активна']).where.not(pid: found_pid).each do |p|
      p.state = 'Приостановлена'
      p.save
    end

    notify(notify_data) unless notify_data.empty? && !Rails.env.development?

  end
end
