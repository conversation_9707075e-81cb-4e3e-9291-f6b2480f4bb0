# frozen_string_literal: true

class RecalcPpPrice
  include SpAuthorize

  def change_collection_rate(pid, cid)
    page = @agent.get("https://www.100sp.ru/ajax/collectionModal?pid=#{pid}&cid=#{cid}&action=/org/collection/edit/#{cid}")

    page.forms[0].field_with(:name => 'Collections[fee]').value = ''
    page = @agent.submit(page.forms[0], page.forms[0].buttons.first)
  end

  def set_price(gid, price)
    params = { 'gid' => gid, 'data' => price, 'type' => 'price' }
    page2 = @agent.post('https://www.100sp.ru/org/good/updateAttribute', params, @ajax_headers)
    @log.info page2.body
  end

  def run
    @log = Logger.new('log/pp_price_recalc.txt')
    authorize

    sql = "CREATE TABLE IF NOT EXISTS pp_price_log (gid INT, price_old DECIMAL(7,2), price_new DECIMAL(7,2), created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP)"
    ActiveRecord::Base.connection.execute(sql)
    @collection_updated = Set.new
    products = []
    ActiveRecord::Base.connected_to(database: :secondary) do
      products = SpProduct.joins(:sp_purchase).where.not(pid: 850710).where('sp_fee>0 and sp_purchases.delivery_days > 0 AND sp_purchases.delivery_days < 5').all
    end
    products.each do |p|
      if p.sp_fee > 0
        r = ActiveRecord::Base.connection.exec_query("SELECT count(*) from pp_price_log WHERE gid=#{p.gid}").rows[0][0]
        next if r > 0
        price = (p.price.to_f * (1.0 + p.sp_fee.to_f / 100)).ceil
        @log.info "#{p.gid} #{p.price} #{price}"
        begin
          set_price(p.gid, price)
          ActiveRecord::Base.connection.exec_query("INSERT INTO pp_price_log (gid,price_old,price_new) VALUES ($1,$2,$3)", 'SQL', [[nil, p.gid], [nil, p.price], [nil, price]])
          unless @collection_updated.include?(p.cid)
            change_collection_rate(p.pid, p.cid)
            @collection_updated.add(p.cid)
          end
        rescue Mechanize::ResponseCodeError => exception
          @log.info "exception"
          puts exception
        end
        # break
      end
    end

  end
end
