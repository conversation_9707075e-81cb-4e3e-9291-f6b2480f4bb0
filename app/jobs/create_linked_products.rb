class CreateLinkedProducts
  include SpAuthorize

  API_KEY = '19d47a1175b3518be54cdb7a0a34592dc79dc663069c42d58314cc21374f752e'

  def initialize
    @agent = Mechanize.new
    authorize

    @pids = []
    if Rails.env.development?
      @db = Mongo::Client.new('mongodb://192.168.99.241:27017/sp_reports')
    else
      @db = Mongo::Client.new('mongodb://127.0.0.1:27017/sp_reports')
    end

    @active_products={}
    @link_data={}

  end

  def find_related_products(user_id,order_time)
    time1=order_time-30.minutes
    time2=order_time+30.minutes
    @db[:orders].find({user_id: user_id, created: {'$gte': time1, '$lte': time2}})
  end

  def run
    data={}
    processed= {}

    buy_together={}

    @db[:orders].find().each_with_index do |order, idx|
      puts "Processed #{idx}" if idx%10000==0
      next if processed[order['oid']]
      key=order['megaorder_id']
      if data[key].nil?
        data[key]=[]
      end
      #data[key] << order
      processed[order['oid']]=1
      products=find_related_products(order['user_id'],order['created'])
      products.each {|p| processed[p['oid']]=1}
      data[key]=products.to_a
      #break if processed.count>100000
    end

    data.each do |k, d|
      d.each do |product|
        key=[product['mega_id'],product['articul']]
        buy_together[key]=Hash.new(0) if buy_together[key].nil?
        d.each do |y|
          key2=[y['mega_id'],y['articul']]
          next if key==key2
          buy_together[key][key2]+=1
        end
      end
    end

    buy_together.each do |prod,bought_together|
      t=bought_together.to_a
      t=t.select {|el| el.last>1}.sort_by(&:last).reverse
      if t.count>0
        @link_data[prod]=t
        puts "#{prod[0]} - #{prod[1]}"
        puts t.map {|e| "#{e[0]} - #{e[1]}"}.join(', ')
        puts ''
      end
    end

    load_active_products

    link_products

  end

  def get_active_pids
    page = @agent.get('http://www.100sp.ru/org/purchase/index')

    ret=[]
    page.search('table.report tr')[1..-1].each do |tr|
      next if tr.search('>td').count!=6
      purch_href=tr.search('td a')[0].attr('href')
      purch_id=0
      if /(\d+$)/=~purch_href
        purch_id=$1.to_i
      end

      state=tr.search('td')[3].text.to_s
      next unless ['Сбор заказов','Дозаказ'].include? state
      #next unless ['Сбор заказов','Активна'].include? state

      page2=@agent.get purch_href
      mid=page2.search('.purchase-actions a').first.attr('href').gsub(/\D/,'').to_i

      ret << [purch_id,mid]
    end
    ret
  end

  def process_col(cid,mid)
    page = @agent.get("https://www.100sp.ru/org/collection/edit/#{cid}")

    #puts cid
    page.search('ul#goods > li').each do |li|

      gid=li.attr('data-gid')
      next if gid=='0'
      art=li.search('input.goods-articul').attr('value').to_s.strip
      @active_products[[mid,art]]=gid
    end
  end


  def process_purchase(pid,mid)
    return if @db[:linkedPurchases].find({ purchaseId: pid }).first

    page = @agent.get("http://www.100sp.ru/purchase/#{pid}")
    page.search("//div[contains(@class,'purchase-mini-collections')]/div/@id").each do |cid|
      next if cid.to_s=='hits'
      process_col(cid,mid)
    end

    @db[:linkedPurchases].insert_one({ purchaseId: pid })
  end

  def load_active_products
    purchases=get_active_pids
    purchases.each do |(pid,mid)|
      process_purchase pid,mid
    end
  end

  def delete_all_bindings(gid)
    ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest', 'Accept' => '*/*'}
    page=@agent.post 'https://www.100sp.ru/org/connectedGoods/index',{gid:gid}, ajax_headers
    sleep(0.2)
    page.search('.connected-good-row').each do |row|
      gid2=row.attr('data-gid').to_s
      @agent.post 'https://www.100sp.ru/org/connectedGoods/removeConnectedGood',{gid:gid,connectedGid:gid2 },ajax_headers
      sleep(0.2)
    end
  end

  def bind_product(gid, gid2)
    ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest', 'Accept' => '*/*'}
    @agent.post 'https://www.100sp.ru/org/connectedGoods/addConnectedGood',{gid:gid,connectedGid:gid2 },ajax_headers
    sleep(0.2)
  end

  def link_products
    @active_products.each do |prod,gid|
      if @link_data[prod]
        delete_all_bindings(gid)
        linked_count=0
        @link_data[prod].each do |link|
          if @active_products[link[0]]
            linked_count+=1
            puts "Link #{prod} (#{gid}) with #{link} (#{@active_products[link[0]]})"
            bind_product(gid, @active_products[link[0]])
          end
          break if linked_count==3
        end
      end
    end
    #binding.pry
  end

end


  #https://www.100sp.ru/org/connectedGoods/addConnectedGood
  # https://www.100sp.ru/org/connectedGoods/removeConnectedGood
#gid: 755079900
#connectedGid: 815454729
  #
  # https://www.100sp.ru/org/connectedGoods/index
  # gid: 755079881
  # <div class="connected-good-row" data-gid="822804856">
  #


