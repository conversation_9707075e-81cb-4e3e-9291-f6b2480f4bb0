# encoding: utf-8

module Simaland
  # todo: auto apply category
  #
  class SimalandParser2 < Downloader
    include TableUtils
    include InvoiceSima

    attr_accessor :sids

    JWT_TOKEN = '3fa8497049787eb60a7351be5fa6d85175a2e57af3b69b55a9615db2efeac360e838e6bcaf54aa8ffae9370a509339d6d12d0344c25d9e35a23da5da63fe3808'

    def initialize
      super

      @dlparams = { 'sids' => [:text, 'Список артикулов'],
                    'brands' => [:text, 'Список кодов брендов'],
                    'series' => [:text, 'Список серий'],
                    'cats' => [:text, 'Список категорий'],
                    'exclude_cats' => [:text, 'Исключить категории'],
                    'exclude_brands_local' => [:text, 'Исключить бренды SIMA из этой закупки'],
                    'top_brands' => [:text, 'Выделить бренды'],
                    'only_stm' => [:checkbox, 'Только СТМ'],
                    'include_adult' => [:checkbox, 'Добавлять товары для взрослых'],
                    'is_enough' => [:checkbox, 'Только большие остатки'],
                    'only_wholesale_cat' => [:text, 'Только товары этой оптовой категории'],
                    'skip_spup_purchase' => [:text, 'Исключить товары из этих закупок в spup'], }

      @added = Set.new

      # @brands = {}

      @categories = {}

      @default_brand = 'СИМА-ЛЕНД'

      @token = ''

      @exclude_in_winter = [944, 945, 948, 956, 957, 1192, 1193]

      @is_winter = (DateTime.now.month > 10 or DateTime.now.month < 4)
    end

    @@include_attrs = ['Цвет', 'Плотность, г/м²', 'Размер, см', 'Вес брутто', 'Состав', 'Длина', 'Материал',
                       'Цвет свечения', 'В наборе, шт.', 'Диаметр шара, см', 'Тип питания', 'Типоразмер батареек',
                       'Количество светодиодов', 'Ткань', 'Рост, см', 'Ширина', 'Высота',
                       'Количество игроков', 'Возраст', 'Количество колёс', 'Maксимальная нагрузка, кг',
                       'Maксимальная нагрузка, кг', 'Материал колёс', 'Материал деки', 'Формат',
                       'Особенности', 'Вес нетто', 'Добавки', 'Вид чая', 'Фасовка',
                       'Добавки чая', 'Диаметр, см', 'Форма', 'Вид тарелки', 'Можно мыть в посудомоечной машине',
                       'Можно использовать в СВЧ-печи', 'размер', 'вес', 'масса', 'Количество ламп',
                       'Режим работы гирлянды', 'Вид лампы', 'Количество режимов', 'Цвет нити гирлянды',
                       'Материал нити', 'Степень защиты', 'Вид светодиода', 'Вид насадки', 'Вид гирлянды',
                       'Объём, мл', 'Рисунок', 'Наличие губки/щётки', 'Назначение', 'Настенные', 'Плотность наполнителя',
                       'Материал наполнителя', 'Состав ткани', 'Сезон', 'Хлопковый чехол', 'Насечки', 'Вибрац', 'шаров',
                       'Диаметр', 'пульт', 'управле', 'Покрытие', 'двер', 'корзи', 'тип', 'разобр', 'устан', 'число', 'наличие', 'толщина', 'глубина',
                       'ящик', 'мощность', 'цоколь', 'Напряжение'
    ].map(&:downcase)

    def self.include_attrs
      @@include_attrs
    end

    def get_barcodes
      sids = Set.new
      Purchase.where(dlclass: 'Simaland::SimalandParser').each do |p|
        Product.where(purchase_id: p.id).where("updated_at>'2022-04-01'").each do |prod|
          if (prod.barcodes == nil) || (prod.barcodes.count == 0)
            sids.add prod.art
          end
        end
      end; nil

      sids.each_slice(50) do |s2|
        res = SimaApi.get "https://www.sima-land.ru/api/v3/item/",
                          { 'per-page': 100,
                            sid: s2.join(','),
                            expand: 'barcodes,all_categories' },
                          nil

        items = JSON.parse(res.body)
        items['items'].each do |i|
          puts i['sid']
          Product.where(art: i['sid']).each do |pr|
            use_sizes = false
            use_sizes = true if i['all_categories'].include? '3515'
            size = '-'
            if use_sizes && (i['modifier_id'] == 91 || i['modifier_id'] == 114 || item['modifier_id'] == 157 || item['modifier_id'] == 97)
              size = i['modifier_value']
            end

            i['barcodes'].each do |code|
              Barcode.find_or_create_by(product_id: pr.id, code: code, size: size)
            end
          end
        end
      end
    end


    def get_cat_tree(cat_id)
      @all_cats[cat_id]['path'].map { |c_id| @all_cats[c_id]['name'] }
    end

    def queue_product(item)
      @product_queue.push(item)
    end

    def save_product(item)
      data = item #.clone
      #data.slice!('id', 'modifier', 'attrs', 'name','parent_item_id')

      brand = 'Сималенд'
      brand = item['trademark']['name'].strip if item['trademark']
      brand_id = nil
      brand_id = item['trademark']['id'].to_i if item['trademark']

      brand = 'Farmstay' if brand == 'Farm Stay'
      brand = 'Mizon' if brand == 'MIZON'

      @log.info "Brand: #{brand},#{brand_id}"
      if @skip_brands.any? { |b| b.gsub(' ', '') == brand.downcase.gsub(' ', '') } || @skip_brands.any? { |b|
        b.to_i == brand_id
      }
        @log.info "Skipping brand"
        return
      end

      p = SimaProduct.find_by(sid: item['sid'])
      if p
        p.data = data
        p.downloaded_at = Time.now
        p.disabled = false
        p.buy_price = item['price']
        p.wholesale_price = item['wholesale_price']
        p.rrp = [item['retail_price'].to_f,item['price_max'].to_f].max
        p.save
      else
        SimaProduct.create(sid: item['sid'],
                           data: data,
                           downloaded_at: Time.now,
                           price: item['price'],
                           disabled: false,
                           wholesale_price: item['wholesale_price'],
                           rrp: [item['retail_price'].to_f,item['price_max'].to_f].max
        )
      end

      @added.add(item['sid'])
    end


    def process_cat(href)
      # puts "Cat #{href}"
      page = @agent.get href

      page.search('.category-list > ul > li > a').each do |a|
        process_cat(a.attr('href').to_s + '&limit=1000')
      end

      page.search('ul.prodList h5 a').each do |a|
        process_page(a.attr('href').to_s)
      end
    end

    def process_cat_page(data)
      data.each do |cat|
        cat['path'] = cat['path'].split('.').map(&:to_i) if cat['path']
        cat['products'] = []
        @all_cats[cat['id']] = cat
      end
    end

    def load_cat_tree_locked
      r = nil
      File.open(Dir.tmpdir + "/sima_cat_tree.lock", "a") do |file|
        file.flock(File::LOCK_EX)
        r = load_cat_tree
        file.flock(File::LOCK_UN)
      end
      r
    end

    def load_cat_tree
      cur_page = 1

      @all_cats = {}
      while true
        begin
          res = SimaApi.get "https://www.sima-land.ru/api/v5/category?p=#{cur_page}", {}, nil, { 'x-api-key' => @token }
          # sleep(0.2)
        rescue Exception => e
          @log.info e&.page&.body
          sleep(20)
        end

        cur_page += 1
        data = JSON.parse(res)
        break if data.length == 0

        process_cat_page data
      end
      @all_cats
    end

    def load_cat_tree_cached
      @all_cats = Rails.cache.fetch("sima_cat_tree", expires_in: 1.day) do
        if Rails.env.development?
          @all_cats = load_cat_tree
        else
          @all_cats = load_cat_tree_locked
        end
        @all_cats
      end

      if @all_cats.empty?
        if Rails.env.development?
          @all_cats = load_cat_tree
        else
          @all_cats = load_cat_tree_locked
        end
        Rails.cache.write("sima_cat_tree", @all_cats, expires_in: 1.day)
      end
    end


    def load_goods
      last_id = 0

      @include_adult = true

      agent = Mechanize.new
      tt = Time.now

      cnt = 0
      loop do
        retry_count = 2
        begin
          @log.info "gt than #{last_id}"

          puts  "#{Time.now - tt} before request"

          params = { 'per-page': 100,
                     'id-greater-than': last_id,
                     with_adult: @include_adult ? 1 : 0,
                     expand: 'all_categories,volume_discounts,description,photo_sizes,ext_description,materials,attrs,barcodes,complete_set_description,notices_text_all,boxtype,files',
                     'is_remote_store': 0 }

          res = agent.get("https://www.sima-land.ru/api/v3/item/",
                          params,nil,
                          {'x-api-key'=>Simaland::SimalandParser::JWT_TOKEN}
          ).body

        rescue Mechanize::ResponseCodeError => exception
          @log.info exception
          @log.info exception.page.body
          if exception.response_code == '404'
            break

          else
            if exception.response_code == '500' or exception.response_code == '403'
              puts '500/403'
              sleep(30)
              retry_count -= 1
              retry if retry_count >= 0
            end

            puts exception.to_s
            puts exception.backtrace

            b = exception.page.body.force_encoding('utf-8')

            File.open('/tmp/sima_error', 'w') { |file| file.write(b) }
            return
          end
        end
        puts  "#{Time.now - tt} after request"

        # puts res
        return if res == '404'

        items = JSON.parse(res)
        @log.info "Items count #{items['items'].count}"


        items['items'].each do |item|
          # @log.info item
          last_id = item['id']
          # add_product(item)
          @pool.post do
            save_product(item)
          end
        end

        cnt += 1
        puts  "#{Time.now - tt} after save"
        puts "#{@pool.queue_length} queue, #{@pool.completed_task_count} completed, #{cnt} pages processed, #{last_id} last"


        #puts last_id

        # sleep(0.02)
        break if items['items'].count == 0
      end
    end


    def get_shipping_cost(sids)
      d = { items: sids, settlement_id: 27503886 }.to_json
      res = @agent.post("https://www.sima-land.ru/api/v3/delivery-calc/", d,
                        { 'Accept' => 'application/json', 'Content-Type' => 'application/json' })

      JSON.parse(res.body)
    end

    def apply_shipping_costs
      sids = @added.map { |s| { sid: s, qty: 1 } }

      @log = Logger.new(STDOUT) unless @log

      @log.info "Start apply shipping costs"

      pool2 = Concurrent::FixedThreadPool.new(10)

      begin
        res = {}
        i = 0
        sids.each_slice(5000) do |sids_part|
          res = get_shipping_cost(sids_part)
          i += 1
          puts "Shipping costs #{i} of #{sids.count / 5000}, wating in pool: #{pool2.queue_length}}"

          res.each do |sid, d|
            pool2.post do
              p = SimaProduct.find_by_sid(sid)
              next unless p
              p.shipping_cost = d['cost']
              p.save
            end
          end

          # sleep(0.2)
        end
      rescue Exception => e
        @log.info e.to_s
        # puts e.page.body
      end

      @log.info "Loaded shipping costs"

      pool2.shutdown
      pool2.wait_for_termination

      @log.info "Shipping costs applied"
    end

    def load_skip_products(pid)
      @exclude_products += Product.where(purchase_id: pid, disabled: false).all.map { |pr| pr.art.to_s }
    end

    def run
      if @agent == nil then
        before
      end

      @global_skip_brands = ['TORNADICA', 'INNAMORE', 'INCANTO', 'Omsa', 'MALEMI', 'GLAMOUR', 'Aravia Professional', 'Aravia Organic']

      @log = Logger.new("log/dl-#{self.class.name.gsub('::', '_')}_#{@purchase.id}.txt")

      @token = JWT_TOKEN

      #load_cat_tree_cached # unless Rails.env.development?

      @skip_brands = Brand.where(skip: true).map(&:brand_name)

      if @global_skip_brands
        @skip_brands += @global_skip_brands
      end

      if @exclude_brands_local
        @skip_brands += @exclude_brands_local.split(',')
      end

      @skip_brands = @skip_brands.map { |s| s.strip.downcase }.select { |s| not s.blank? }.uniq

      @log.info "Skip brands: #{@skip_brands}"

      @pool = Concurrent::FixedThreadPool.new(20)

      start_download_time = Time.now
      load_goods
      @pool.shutdown
      @pool.wait_for_termination

      SimaProduct.where("downloaded_at < ?", start_download_time).update_all(disabled: true)
      SimaProduct.where(downloaded_at:nil).update_all(disabled: true)


      apply_shipping_costs
    end
  end
end
