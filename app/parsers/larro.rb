#encoding: utf-8

require 'downloader'
#require 'logger'
#require 'unicode_utils/downcase'

class Larro < Downloader
  attr_accessor :login
  attr_accessor :password

  def initialize
    @dlparams={'login'=>:text,'password'=>:password}
    super
  end


  def processPage(url)
    tries ||= 2

    @log.info('page '+url)

    puts url

    page = @agent.get(url)
    conv=Encoding::Converter.new('Windows-1251','UTF-8')
    body=conv.convert(page.body)
    page = Nokogiri::HTML(body)

    if /productId=([0-9]+)/=~url
      art1=$1
    else
      return
    end

    puts art1

    name=page.search('.title')[0].text

    t=page.search('.info')[0].text
    if /Артикул: (.*)/=~t
      art=$1.strip
    end

    sost=''
    if /Состав:(.*)/=~t
      sost=$1.strip
    end
    sost.gsub!(/Артикул.*/,'')

    discount=false
    if page.search("#prise_wholesale span#product_sale_new").length>0
      price=page.search("#prise_wholesale span#product_sale_new").text.strip
      discount=true
    else
      price=page.search("#prise_wholesale  .prise").text.strip
    end

    cat=page.search('ul#sector_general li.selected a').text

    if discount then cat=cat+" - скидки" end

    pic_urls=page.search('.list_photos img').map {|img| img.attr('src').gsub('_60_105','_800_1400')}

    desc=page.search('.txt').text.strip
    desc+=' Состав: '+sost

    pics=[]
    pic_urls.each_with_index do |url,i|
      savePic(url,"#{art1}-#{i}",false)
      pics<<"#{art1}-#{i}"
    end

    page.search('table.item_grid >tr').each_with_index do |tr,i|
      color=tr.search('.color_title').text.to_s
      sizes=tr.search('td.title_and_items div.size_row_1').map(&:text)

      if tr.search('td.color_image img').length>0 and i>0
        picurl=tr.search('td.color_image img')[0].attr('src')
        puts picurl
        savePic(picurl,"#{art1}-color-#{i}",false)
        pics<<"#{art1}-color-#{i}"
      end
      col=addCollection(cat,'850')
      desc2=desc
      desc2='Цвет '+color+'. '+desc if not color.nil? and not color==''

      addProduct(col,"#{art1} #{art}",color,price,desc2,sizes,pics)
      desc=''
      pics=[]
    end


  rescue Mechanize::Error => e
    puts e
    @log.info e
    print e.backtrace.join("\n")
    retry unless (tries-=1).zero?

  end

  def processCat(href)
    tries ||= 2
    puts href
    @log.info('cat '+href)
    page = @agent.get(href)

    page.search("#content_center_container .product_collection_item .product_img a").each do |a|
      processPage(a['href'])
    end

  rescue Mechanize::Error => e
    @log.info e
    puts e
    print e.backtrace.join("\n")
    retry unless (tries-=1).zero?

  end

  def run
    page = @agent.get('http://larro.ru/?view=internet_shop&idStore=1')

    login_form=page.forms[2]
    login_form.e_mail=@login
    login_form.password=@password

    page = @agent.submit(login_form, login_form.buttons.first)

    page = @agent.get('http://larro.ru/?view=internet_shop&idStore=2')


    page.search("ul#sector_general li a").each do |a|
      processCat(a['href'])
    end

  end
end