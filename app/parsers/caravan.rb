#encoding: utf-8

require 'downloader'

class Caravan < Downloader

  def initialize
    @dlparams={}

    super
  end


  def processProd(li,cat)

    name=li.search('p.title a').text.strip

    art1=li.search('input[name="id"]')[0].attr('value')

    return if product_added? art1

    art=''
    if %r!<p>Артикул:<span class="black">(.*?)</span>!=~li.inner_html
      art=$1
    end

    desc=''
    if %r!<p>Описание:<span class="black">(.*?)</span>!=~li.inner_html
      desc=$1
    end


    puts cat
    puts art
    puts art1


    price=li.search('div.price span.red')[0].text.gsub(' руб.','').strip.to_i
    return if price==0
    puts price

    min_order=1
    t=li.search('a.text_basket').attr('href').to_s
    if /, ([0-9])+/=~t
      min_order=$1.to_i
    end

    pics=[]
    li.search('div.gallery a.actives').each_with_index do |a,i|
      url=a.attr('href')
      savePic(url,"#{art1}~#{i}",false)
      pics<<"#{art1}~#{i}"
      break
    end

    sizes=[min_order]


    col=addCollection(cat,"4686")
    addProduct(col,art1,art,price,name+'. '+desc,sizes,pics)
  end

  def processCat(url)
    @log.info(url)
    page = @agent.get(url)

    page.search(".posit_cat a").each do |a|
      processCat(a.attr('href'))
    end

    cat=page.search('li.act a')[0].text.strip

    page.search("li.list_product").each do |li|
      processProd(li,cat)
    end

  end

  def run
    page = @agent.get('http://www.karavanmarket.ru/')

    page.search("ul.menu_cat").each do |a|
      processCat(a.attr('href'))
    end

  end
  #handle_asynchronously :run

end