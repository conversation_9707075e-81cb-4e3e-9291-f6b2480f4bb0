#encoding:utf-8
require_relative '../framework/site/product_config'
module ItParad
  class ItParadProductConfig < Framework::Site::ProductConfig
    def get_name_from_description(description)
      return 'Сумка' if description.include? 'сумк'
      return 'Рюкзак' if description.include? 'рюкзак'
      return 'Сумочка' if description.include? 'сумочк'
      return 'Планшет' if description.include? 'планшет'
      return 'Клатч' if description.include? 'клатч'
      return 'Барсетка' if description.include? 'барсетк'
      return 'Портфель' if description.include? 'портфел'
      return 'Ремень' if description.include? 'ремн'
      'Зонт' if description.include? 'зонт'
      ''
    end

    def parse_product(product_page, link=nil)
      result = super(product_page, link)
      span = product_page.search('#reduction_amount_display').first
      is_new = (span.nil? or span.text.blank?)
      result.category = "#{result.category} - Распродажа" unless is_new
      result.category = "#{result.category} - Новинки" if is_new
      result.name = prepare_string "#{get_name_from_description(result.description)} #{result.name}"
      result
    end

    def create_new_product(product_elem, link = nil)
      products = parse_product(product_elem, link)
      @purchase.check_stop
      if products.nil?
        @error_log_func.call "Product is nil for link #{link} and element #{product_elem}"
        return
      end
      products = [products] unless products.kind_of? Array
      products.each { |product|
        log_product(product) if @with_logging
        if is_product_unique(product)
          add_product(product)
        end
      }
    end
  end
end