#encoding:utf-8
require_relative '../framework/order_sender'

module AlisaFashion
  class AlisaFashionOrderSender < Framework::OrderSender
    def clean
      basket_page = @agent.get('http://alisafashion.ru/personal/cart/')
      urls = basket_page.search('.line a.remove').map{|a|a.attr('href').to_s}

      urls.each{|url|@agent.get(url)}
    end

    def order(articul, sizes)
      puts articul, sizes
      search_page = @agent.get('http://alisafashion.ru/search/index.php?q='+articul)

      links = search_page.search('.product a')

      if links.length == 0
        add_error_csv_data('Cannot find product', articul)
        return
      end

      if links.length > 1
        add_error_csv_data('Find more than one product', articul)
        links = links.to_a.find_all{|l|prepare_string(l.search('h2').first.text) == articul}
      end

      if links.length > 1
        add_error_csv_data('Find more than one product again', articul)
      end

      link = links[0].attr('href').to_s
      product_page = @agent.get(link)

      sizes.each do |size, quantity|
        puts size
        label = product_page.search('.sizes > label').find{|lbl|(prepare_string lbl.text) == size}
        if label.nil?
          add_error_csv_data('Cannot find label with this size', articul, nil, size, quantity)
          next
        end

        input = product_page.search("##{label.attr('for').to_s}:not([disabled])").first

        if input.nil?
          add_error_csv_data('Cannot find input with this size', articul, nil, size, quantity)
          next
        end

        size_id = input.attr('value').to_s

        quantity.times{
          @agent.get("#{link}?action=ADD2BASKET&ajax_basket=Y&id=#{size_id}")
        }
      end

    end

    def add_csv_data(fields)
      articul = fields['Наименование'].to_s
      size = fields['Размер'].to_s

      @csv_data[articul] = {} unless @csv_data.include? articul

      @csv_data[articul][size]+=1 if @csv_data[articul].include? size
      @csv_data[articul][size]=1 unless @csv_data[articul].include? size
    end
  end
end