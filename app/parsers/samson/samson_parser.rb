#encoding:utf-8
require_relative '../framework/parser'
require_relative '../framework/parsed_product'
require_relative 'samson_product_config'
require 'spreadsheet'

module Samson
  class SamsonParser < Framework::Parser

    def initialize

      @price=Hash.new
      @category_map = Hash.new
      @skip_categories=[]

      super
    end

    def initialize_settings

      with_auth_data(->(ad) {
        ad.with_auth_url('http://www.samsonopt.ru/zakaz/')
            .with_login('nsf111813')
            .with_password('270279')
            .with_login_field_name('USER_LOGIN')
            .with_password_field_name('USER_PASSWORD')
            .with_form(->(page) {
              page.forms[1]
            })
      })

      with_url('http://www.samsonopt.ru/zakaz/')
          .with_category_links('#catalogNav > li > a:not(.setsLink)')
          .with_pagination('.catalogListInfo:not(.bottom) .paginator > li:last-child > a')
          .with_product_config(->(pc) {
            pc.with_product_selector('#catalogListItems h5 > a')
                .with_category(->(product_page) {
                  product_page.search('#breadcrumbs li a').drop(1).map { |li| prepare_string li.text }.join '->'
                })
                .with_articul(->(product_page){
                  /CODE=([0-9]+)/=~product_page.uri.to_s
                  $1
                })
                .with_images(->(product_page) {
                  if product_page.search('ul.fancybox_list a').size>0
                    product_page.search('ul.fancybox_list a').map { |a| a.attr('href').to_s }
                  elsif product_page.search('a.Product__imageLink').size>0
                    [product_page.search('a.Product__imageLink')[0].attr('href').to_s]
                  else
                    return [product_page.search('div#itemImage a')[0].attr('href').to_s] if page.search('div#itemImage a').size>0
                    []
                  end
                })
                .with_description(->(product_page) {
                  description=product_page.search('#tabDescription .text p').text.strip
                  params=''
                  params=product_page.search('#tabDescription ul li')[1..-1].map(&:text).join ' ' if product_page.search('#tabDescription ul li').size>1
                  description + params
                })
                .with_image_config(->(ic) {
                  ic.with_default_url('http://www.samsonopt.ru/')
                })
                .with_category_type('100000')
                .with_excel_products(->() { @excel_products })
          }, Samson::SamsonProductConfig)
    end

    def process
      read_cat_map

      download_file
      book = Spreadsheet.open '/tmp/samson.xls'

      read_sale book
      @log.info @price

      @excel_products = read_file book

      super
    end


    def process_category(link)
      page = get_page link
      inner_links = page.search('dd > ul > li > a').map{|a|a.attr('href').to_s}

      inner_links.each {|link| super(link)}
    end

    def download_file
      @agent.download('http://www.samsonopt.ru/zakaz/services/prices/?price=stock', '/tmp/samson.zip')

      file_deleted = false

      #hello, windows
      while !file_deleted and File.exist? '/tmp/samson.xls'
        begin
          File.unlink '/tmp/samson.xls'
          file_deleted = true
        rescue
        end
      end

      Zip::File.open("/tmp/samson.zip") do |zipfile|
        zipfile.each do |entry|
          entry.extract('/tmp/samson.xls')
          break
        end
      end
    end

    def read_cat_map
      book = Spreadsheet.open '/home/<USER>/samson_map.xls'

      sheet = book.worksheet 0
      sheet.rows.each do |row|
        cat=row[0]
        cat2=row[1]
        cat_id=row[2]

        cat_id='100000' if cat_id.nil?
        cat2=cat if cat2.nil?

        if cat2=='УБРАТЬ'
          @skip_categories<<cat
          next
        end

        @category_map[cat] = {'name' => cat2, 'id' => cat_id}
      end

    end

    def read_sale(book)
      sheet = book.worksheet 'Снижение цен'

      sheet.rows[3..-1].each do |row|
        price=row[4]
        rate=row[5]
        next if rate.nil?
        art=row[0]
        puts art
        next if rate<25

        q=1.15
        q=1.25 if rate>40

        @price[art]=(price*q).ceil
      end
    end

    def read_file(book)
      result = []
      sheet = book.worksheet 'Прайс-заказ'

      start_row=0
      sheet.rows.each_with_index do |row, i|
        if row[0]=='Код'
          start_row=i+3
          break
        end
      end

      puts start_row

      category=''
      sheet.rows[start_row..-1].each do |row|
        articul=row[0]
        next if articul.nil?
        articul=articul.to_s.gsub('.0', '')
        puts articul
        @log.info articul

        name=row[1]

        price=row[5]
        if price.nil?
          category=name
          next
        end

        price=price.ceil
        @log.info "Price #{price}"
        stock=row[30]

        @log.info "Stock #{stock}"

        next if stock<1

        min_order=row[6].split('/')[0].to_i
        @log.info "Min order #{min_order}"

        if @skip_categories.include? category
          @log.info 'Skip cat'
          next
        end

        category_id = '100000'
        if @category_map.include? category
          category_id=@category_map[category]['id']
          category=@category_map[category]['name']
        end

        is_sale=false

        if @price.has_key? articul
          is_sale=true
          price=@price[articul]
          @log.info "Price sale #{price}"
        end

        @log.info "Cat name #{category}"

        description=''
        description="Упаковка #{min_order} шт. Цена за 1." if min_order>1

        result << Framework::ParsedProduct.new(category, articul, [], name, description, price, {'is_sale' => is_sale, 'category_id' => category_id})
      end
      result
    end
  end
end
