module Gretta
  class GrettaParser < Downloader
    include TableUtils

    attr_accessor :price_file
    attr_accessor :user_login
    attr_accessor :user_password

    def initialize
      @dlparams={'price_file'=>:file}

      # Учетные данные для авторизации - заполните вручную
      @user_login = '<EMAIL>'
      @user_password = 'En2V3efB'

      super
    end

    def login
      return if @user_login.blank? || @user_password.blank?

      @log.info "Logging in to Gretta.ru"
      page = @agent.get('https://gretta.ru/personal/')
      login_form = page.form_with(:name => 'form_auth')

      if login_form
        login_form['USER_LOGIN'] = @user_login
        login_form['USER_PASSWORD'] = @user_password
        result_page = @agent.submit(login_form, login_form.buttons[0])
        @log.info "Login completed"
      else
        @log.error "Login form not found on https://gretta.ru/personal/"
      end
    end

    def process_file(f)
      workbook = Roo::Spreadsheet.open(f.to_s)

      workbook.sheets.each do |sheet_name|
        sheet = workbook.sheet(sheet_name)

        name_col = get_col_number(sheet, ['номенклатура'])
        art_col = get_col_number(sheet, 'артикул')
        retail_price_col = get_col_number(sheet, 'первая цена')
        wholesale_price_col = get_col_number(sheet, ['цена предоплаты','цена со скидкой'], find_last: true)

        sheet.each do |r|
          next if r.nil?

          name = r[name_col]
          next if name.nil?

          art = r[art_col]
          next if art.nil?

          price = r[wholesale_price_col]
          rrp = r[retail_price_col]

          price_product = {
            name: name,
            art: art,
            price: price,
            rrp: rrp
          }

          @price_products << price_product
        end
      end
    end

    def get_sizes(page)
      sizes = ['-']
      if page.at('.item__aside button[data-dialog="#dialog_l"]')
        sizes = []
        href = page.at('.item__aside button[data-dialog="#dialog_l"]').attr('data-dialog-fetch').to_s
        sleep(0.2+rand(0.4))
        
        begin
          page2 = @agent.get(href)
          page2.search('div[data-product-id]').each do |div|
            s = div.at('.product-table__price-value').text.strip
            stock = div.at('.product-table__availability').text.strip.to_i
            next if stock <= 0
            sizes << s
          end
          
          # Sort sizes - handle both numeric and letter sizes (XXS,XS,S,M,L,XL,XXL)
          size_order = { 'XXS' => 1, 'XS' => 2, 'S' => 3, 'M' => 4, 'L' => 5, 'XL' => 6, 'XXL' => 7 }
          sizes.sort_by! do |size|
            if size =~ /^\d+$/  # If size is numeric
              [0, size.to_i]    # Sort numeric sizes first, by their numeric value
            elsif size_order.key?(size)
              [1, size_order[size]]  # Sort letter sizes second, by their predefined order
            else
              [2, size]  # Sort any other format last, alphabetically
            end
          end
        rescue Mechanize::ResponseCodeError => exception
          @log.error "Error fetching sizes from #{href}: #{exception}" if defined?(@log)
          sizes = []
        rescue => e
          @log.error "Unexpected error fetching sizes from #{href}: #{e}" if defined?(@log)
          sizes = []
        end
      end
      sizes
    end

    def get_pics(page)

    end

    def get_description(page)
      desc = ""

      # Find the tabs list
      tabs_list = page.at('div.tabs__list')

      if tabs_list
        # Find all button elements in the tabs list
        tab_buttons = tabs_list.search('button')
        description_tab_index = -1

        # Find the "Описание" tab and its index
        tab_buttons.each_with_index do |button, index|
          span = button.at('span')
          if span && span.text.strip == "Описание"
            description_tab_index = index
            break
          end
        end

        # If found, get the corresponding content div
        if description_tab_index != -1
          content_divs = page.search('div.tabs div.info__content')
          if content_divs.length > description_tab_index
            desc = content_divs[description_tab_index].text.strip
          end
        end
      end

      desc
    end

    def process_page(href, prefix)
      #href = 'https://gretta.ru/catalog/item-559772/'
      # Create cache directory if it doesn't exist
      cache_dir = Rails.root.join('cache', 'gretta_pages')
      FileUtils.mkdir_p(cache_dir) unless File.directory?(cache_dir)
      
      # Create a cache filename based on the URL (sanitize the URL to be safe for filenames)
      safe_filename = Digest::MD5.hexdigest(href)
      cache_file = File.join(cache_dir, "#{safe_filename}.html")
      
      page = nil
      
      # Check if cache file exists and is less than 3 days old
      if File.exist?(cache_file) && (Time.now - File.mtime(cache_file)) < 365.days
        puts "Using cached file for #{href}"
        begin
          # Read cached content and parse with Nokogiri
          cached_content = File.read(cache_file)
          page = Nokogiri::HTML(cached_content)
        rescue => e
          puts "Error reading cache file: #{e.message}"
          # If there's an error reading the cache, we'll fetch the page below
          page = nil
        end
      end
      
      # If we don't have a valid page from cache, fetch it
      if page.nil?
        puts "Fetching page #{href}"
        begin
          # Fetch the page
          response = @agent.get(href)
          sleep(0.3 + rand(0.2))
          
          # Handle encoding properly when saving to cache file
          begin
            # Try to convert to UTF-8 with replacement for invalid characters
            content = response.body.encode('UTF-8', 'UTF-8', invalid: :replace, undef: :replace, replace: '?')
            File.write(cache_file, content)
          rescue Encoding::UndefinedConversionError => e
            # Fallback to binary write if encoding conversion fails
            puts "Encoding conversion failed, writing binary: #{e.message}"
            File.binwrite(cache_file, response.body)
          end
          
          # Parse the page
          page = response
        rescue => e
          puts "Error fetching page: #{e.message}"
          return # Skip this page if we can't fetch it
        end
      end

      cat_name = page.at('li#bx_breadcrumb_3 span').text.strip
      name = page.at('h1.content__title').text.strip
      desc = get_description(page)

      @log.info name

      props = page.search('table.info__specs tr').map do |tr|
        k = tr.at('th').text.strip.gsub(':','')
        v = tr.at('td').text.strip
        [k,v]
      end.to_h

      art = props['Артикул']

      #file_product = @price_products.find {|p| p[:name] == name}

      if file_product

        price = file_product[:price]
        rrp = file_product[:rrp]
      else
        price = page.at('.item__price-value').text.strip.gsub(' ', '').to_i
        rrp = (price * 1.4).ceil
      end
      weight = props['Вес'].to_i if props['Вес']

      sost = props['Состав/материал']
      brand = props['Бренд']
      color = props['Цвет']
      name = "#{name}, цвет #{color}" if color

      desc = "#{desc}. Состав: #{sost}" if sost

      page_id = page.at('div.item__action-list').attr('data-id')

      pics_urls = page.search('img.item-gallery__thumbs-image').map {|img| img.attr('data-src').to_s.gsub(/.*?webp\//,'')}

      dl_pics = []
      pics_urls.each_with_index do |p, i|
        dl_pics << savePic(p, "#{page_id}~#{i}", true, true, false, :active, nil, show_order: i)
      end


      sizes = get_sizes(page)
      @log.info sizes

      return if sizes.empty?

      col = addCollection("#{prefix} - #{cat_name}", 0)

      p = addProduct(col, "#{art}/#{page_id}", name, price, desc, sizes, dl_pics, nil, 0, nil, rrp: rrp, weight: weight, brand_name: brand)
    end

    def process_folder_page(page, prefix)
      page.search('.product-list .product-list__item .product__title a').each do |a|
        process_page(a.attr('href'), prefix)
      end

    end
    def process_folder(href, prefix)
      @log.info "Folder #{href}"
      page = @agent.get(href)
      process_folder_page(page, prefix)

      last_page_num = page.search('.pagination a')[-2].text.to_i
      return if last_page_num <= 1

      (2..last_page_num).each do |page_num|
        @log.info "Folder #{href}?PAGEN_1=#{page_num}"
        process_folder_page(@agent.get("#{href}&PAGEN_1=#{page_num}"), prefix)
      end
    end

    def run(file = nil)

      if @agent==nil then before end

      # Выполняем авторизацию если указаны логин и пароль
      login

      @price_products = []

      @price_file = file if file
      process_file(@price_file) if @price_file

      process_folder('https://gretta.ru/catalog/zhenskaya_kollektsiya/?PAGEN_1=1&set_filter=Y&catalogFilter_P177_MIN=&catalogFilter_P177_MAX=&setSort=sort_desc&setView=block&setPage=48','Женщинам')
      process_folder('https://gretta.ru/catalog/muzhskaya_kollektsiya/?PAGEN_1=1&set_filter=Y&catalogFilter_P177_MIN=&catalogFilter_P177_MAX=&setSort=sort_desc&setView=block&setPage=48','Мужчинам')

      ChatGptNameBatch.new.run(@purchase_id, prompt: 'Улучши название товара для интернет-магазина, не включай артикул, сделай название простым и приятным для покупателя:')
      ChatGptWeightBatch.new.run(@purchase_id, use_description: true)

    end


  end
end
