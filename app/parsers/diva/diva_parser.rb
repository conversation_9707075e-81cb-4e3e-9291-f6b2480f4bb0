module Diva
  class DivaParser < Downloader
    include InvoiceFullname

    def initialize
      @dlparams={'price_file'=>:file}

      super
    end


    def process_page(url)
      tries ||= 2

      puts url
      @log.info(url)

      return if @added.include? url
      @added<<url

      page = @agent.get(url)

      cat1=page.search('ul[itemprop=breadcrumb] li')[1].text
      cat=page.search('ul[itemprop=breadcrumb] li').last.text


      if /new window.waTheme.init.shop.Product\((.*?)\);}\)\(jQuery\);/=~page.body
        js=$1
        js.gsub!('$form: $form,','')
        js.gsub!(/(\b[a-z_]+):/, '"\1":')
        data=JSON.parse(js)

        data=data['skus'].values.first

        buy_price=data['mprice_plugin_1'].to_f
        return if buy_price<1
        price=(buy_price*1.2).ceil

        rrp=data['price'].to_f
        art=data['sku']
        size=data['features']['netto'] if data['features'].is_a? Hash

        name=page.at('h1[itemprop=name]').text.gsub(art,'').strip

        desc=''
        desc=page.at('.s-summary').text if page.at('.s-summary')

        desc.gsub!(/\t+/,' ')
        desc.gsub!(/ +/,' ')
        desc.gsub!(/\.+/,'.')
        desc.gsub!(/(\s\.)+/,'')
        desc.strip!

        pic_url=page.at('a#s-photo-main')
        if pic_url
          pic_url=pic_url.attr('href').to_s
          pics=[savePic(pic_url,"#{art}",true)]

        end

        if cat1.include? 'питания'
          cat=guess_food_cat(name)
        end

        cat_id=guest_cat_id(cat,name)

        sizes=[]
        sizes<<size if size

        col=addCollection(cat,cat_id)
        addProduct(col,art,name,price,desc,sizes,pics,nil,0,nil,rrp:rrp,source: url,buy_price:buy_price  )
      end


    rescue Mechanize::Error => e
      puts e
      print e.backtrace.join("\n")
      retry unless (tries-=1).zero?

    end

    def process_cat_data(page)
      page.search('li[data-product-id] h5.s-product-header a').each do |a|
        process_page(a.attr('href').to_s)
      end
    end

    def process_cat(href)
      @log.info "Process cat #{href}"

      page=@agent.get(href)

      process_cat_data(page)

      if page.search('ul.s-paging-list li a').count>1
        last=page.search('ul.s-paging-list li a')[-2].text.to_i
        (2..last).each do |p|
          page2=@agent.get(href+"?page=#{p}")
          process_cat_data(page2)
        end
      end
    end

    def guest_cat_id(cat,name1)
      name=name1.downcase

      unless cat.include? 'питания'
        cat_type=107361
        cat_type=107366 if name.include? 'тела'
        cat_type=108813 if name.include? 'волос'
        cat_type=109580 if name.include? 'ног'
        cat_type=109580 if name.include? 'рук'
        cat_type=107353 if name.include? 'шампу'
        cat_type=108812	 if name.include? 'бальз'
        cat_type=108812	 if name.include? 'кондиц'
        cat_type=2427 if name.include? 'мужс'
        cat_type=107843 if name.include? 'гель для душа'
        cat_type=107843 if name.include? 'мыло'
        cat_type=107353 if name.include? 'шампу'
      else
        cat_type=100306
        cat_type=100362 if name.include? 'чай'
        cat_type=100344 if name.include? 'резинка'
        cat_type=100349 if name.include? 'конфеты'
        cat_type=108547 if name.include? 'кофе'
        cat_type=108545 if name.include? 'зернах'
        cat_type=100358 if name.include? 'напит'
        cat_type=100347 if name.include? 'печенье'
        cat_type=109362 if name.include? 'соус'
      end

      cat_type
    end

    def guess_food_cat(cat,name)
      n=name.downcase

      return 'Напитки' if ['чай','кофе','напит', 'вода','лимонад'].any? {|t| n.include? t}
      return 'Соусы, приправы' if ['соус','приправ','уксус','майон','кетчуп','заправк'].any? {|t| n.include? t}
      return 'Суп, лапша' if ['суп','лапша','мисо', 'вода'].any? {|t| n.include? t}
      return 'Конфеты, печенье' if ['конфет','карамел','печень', 'шоколад','жеватель','мармела','зефир','желе'].any? {|t| n.include? t}
      return 'Крекеры, снеки' if ['крек','снек','капуста'].any? {|t| n.include? t}
      cat
    end

    def process_file(f)
      CSV.foreach(f,:col_sep=>';', :headers => true,:encoding=>'Windows-1251') do |fields|
        cat_code=fields['Код категории'].gsub(/^0+/,'')
        art=fields['Артикул'].gsub(/^'/,'')
        name=fields['Наименование']
        size=''
        if /, ([^,]*$)/=~name
          size=$1
          name.gsub!(/, ([^,]*$)/,'')
        end
        art="#{cat_code}-#{art}-#{size.gsub(/\D/,'')}"
        desc=fields['Описание']
        cat=fields['Категория']
        price=(fields['Цена'].gsub(/[^0-9.,]/,'').to_f*1.2).round
        barcode=fields['Штрихкод'].gsub(/^'/,'')
        pic=fields['Фото']
         cat=guess_food_cat(cat,name)
        cat_id=guest_cat_id(cat,name)
          rrp=(price*1.1).round
        pic=savePic(pic,"#{art}",true)
        col=addCollection(cat,cat_id)
        addProduct(col,art,name,price,desc,[size],[pic],nil,0,nil,rrp:rrp,buy_price:price,barcode:barcode  )
      end
    end

    def run
      @added=[]

      process_file(@price_file)

      #page=@agent.get 'https://xn----8sbehsg5n.xn--p1ai/'
      #page.search('ul.s-catalog-list > li > a').each do |a|
      #        process_cat(a.attr('href').to_s)
      #end

    end
  end
end
