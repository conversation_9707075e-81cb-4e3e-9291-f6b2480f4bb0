module VladiArt
  class VladiArtParser < Downloader
    @dlparams={'login'=>:text,'password'=>:password}

    def initialize
      @dlparams={'login'=>:text,'password'=>:password}
      super
    end

    def process_product(div,cat_name,pic_urls)
      props={}
      div.search('.product__kv .kv__item').each {|div2|
        k=div2.search('label.kv__label').first
        v=div2.search('span.kv__value').first
        if k and v
          props[k.text.strip]=v.text.strip
        end
      }

      art=props['Артикул']

      code=div.search('form').first.attr('action').to_s.gsub(/\D/,'')

      price=(div.search('meta[itemprop=price]').first.attr('content').to_f).ceil
      return if price==0

      name=div.search('meta[itemprop=name]').first.attr('content').to_s

      sizes=[]
      sizes=props['Размеры'].split(',') if props['Размеры']

      desc = ''
      desc = "Цвет: #{props['Цвет']}." if props['Цвет']
      desc = "#{desc} Состав: #{props['Состав']}." if props['Состав']
      desc = "#{desc} Состав: #{props['Сосав']}." if props['Сосав']

      if props['Состав'].nil? && props['Сосав'].nil?
        sost = props.keys.find { |k| k.include? '%'}
        desc = "#{desc} Состав: #{sost}." if sost
      end


      cat_type='20'
      #cat_type='' if name=='Платье'
      #cat_type='' if name=='Жакет'
      #cat_type='' if name=='Брюки'
      #cat_type='' if name=='Рубашка'
      #cat_type='' if name=='Юбка'
      #cat_type='' if name=='Блуза'
      #cat_type='' if name=='Жилет'
      #cat_type='' if name=='Шорты'
      #cat_type='' if name=='Сарафан'

      pics=[]
      pic_urls.each_with_index {|url,i |
        pics << savePic(url,"#{code}~#{i}",true)
      }

      rrp = (price*1.35).round(-2)

      col=addCollection(cat_name,cat_type)
      addProduct(col,code+' '+art,name,price,desc,sizes,pics, nil, 0, nil, rrp: rrp)

    end

    def process_page(url)
      puts url

      page=@agent.get url

      cat=page.search('ul.breadcrumbs__list a.breadcrumbs__link').last.text

      pic_urls=page.search('.product a.colorbox').map{|a| a.attr('href').to_s}.reverse

      page.search('div[itemtype="http://schema.org/Product"]').each{|div|
        process_product(div,cat,pic_urls)
      }
    end

    def process_cat(href, process_pages=true)
      puts "Cat #{href}"
      page=@agent.get href

      page.search('.cat__list .cat__item .prd__pic a.prd__pic-link').each {|a|
        process_page(a.attr('href').to_s)
      }

      pages=page.search('ul.pagination a.pagination__link')
      if pages.count>3 and process_pages
          pages[2..-2].each {|a|
            process_cat(a.attr('href').to_s,false)
          }
      end

    end

    def run
      if @agent==nil then before end

        page=@agent.get 'http://vladi-art.com/'
      login_form=page.form_with(action:'/site/login')
      login_form['LoginForm[username]']=@login
      login_form['LoginForm[password]']=@password
      page = @agent.submit(login_form, login_form.buttons[0])

      page.search('ul.nav-main__list2 a.nav-main__link2').each {|a|
        href=a.attr('href').to_s

        process_cat(href)
      }


    end

  end

end
