
require_relative 'site/product_config'
require_relative 'base_site_job'
require_relative 'file_download_config'
require 'mechanize'
module Framework
  class Parser < Framework::BaseSiteJob
    attr_accessor :is_download_files
    attr_accessor :no_direct_upload
    attr_reader :sleep_time
    attr_reader :price_multiplier
    attr_reader :sale_collection_name
    attr_accessor :upload
    attr_reader :default_brand

    def initialize
      @no_direct_upload=false
      @dlparams = {} if @dlparams.nil?
      #@dlparams = {'is_download_files' => :checkbox}.merge!(@dlparams)

      @upload=false

      @picture_path=Spup2::Application.config.image_path+self.class.name.gsub('::', '_')+'/'
      FileUtils.mkpath(@picture_path) unless File.exist? @picture_path
      @auth_data = nil
      @category_links_getters = []
      @catalog_urls = []
      @product_config = nil
      @category_func = nil
      @additional_category_links = []
      @file_configs = {}
      @download_files = {}
      @parser=nil
      @sleep_time=0
      @product_limit=0
    end

    def initialize_settings

    end

    def pp_object
      nil
    end

    def sku_prefix
      @purchase.dlclass[0..3].upcase.ljust(4,'_')
    end

    def get_sku(product)
      "#{sku_prefix}#{product.id}"
    end

    ### CORE METHODS
    def process
      download_files if @is_download_files

      @catalog_urls.each { |url| process_catalog(url) }
      @additional_category_links.uniq!
      @additional_category_links.each do |link|
        res=process_category(link)
        break if @product_limit>0 and res.count>@product_limit
      end

      raise 'Пустая коллекция товаров' if Purchase.find(purchase_id).collections.empty?
    end

    def download_files
      puts 'start download'
      @file_configs.each do |key, config|
        puts key
        @download_files[key] = config.run
      end
      puts 'end download'
    end

    def process_catalog(catalog_url)
      catalog_page = get_page catalog_url

      category_links = get_category_links(catalog_page)

      category_links.uniq!

      category_links.each do |link|
        res=process_category(link)
        break if @product_limit>0 and res.count>@product_limit
      end
    end

    def process_category(link)
      next_link = link
      result = []
      begin
        @log.info "Process category #{next_link}"
        category_page = get_page next_link

        return result if category_page.nil?

        if @product_limit>0
          return result if result.count>@product_limit
        end
        
        result.concat process_products(category_page)

        next_link = get_next_link(category_page)
      end until next_link.nil?
      result
    end

    def get_next_link(category_page)
      return nil if @next_link_getter.nil?
      return @next_link_getter.call(category_page) unless @next_link_getter.is_a? String

      next_link_elem = @next_link_getter.nil? ? nil : category_page.search(@next_link_getter).first

      return nil if next_link_elem.nil? or next_link_elem.attr('href').nil? or next_link_elem.attr('href').to_s == '#'

      next_link_elem.attr('href').to_s
    end

    def process_products(category_page)
      @product_config.with_category(->(page) { @category_func.call(category_page) }) unless @category_func.nil?
      @product_config.with_picture_path(@picture_path)
      @product_config.run(@agent, category_page)
    end

    def get_category_links(catalog_page)
      @category_links_getters.map { |getter| getter.is_a?(String) ? get_links(catalog_page, getter) : getter.call(catalog_page) }.flatten
    end

    def get_page(link)
      tries=3
      begin
        sleep(@sleep_time+rand(@sleep_time/3)) if @sleep_time>0

        if link.start_with? 'http:'
          ipv4_url = URI.parse(URI::DEFAULT_PARSER.escape(link))
          ipv4_address = Resolv.getaddress(ipv4_url.host)
          domain_name = ipv4_url.host
          ipv4_url.host = ipv4_address
          link = ipv4_url.to_s
          @agent.request_headers = { 'Host' => domain_name }
        end
        @agent.agent.http.verify_mode = OpenSSL::SSL::VERIFY_NONE

        page=@agent.get(link)
        if page.encoding == 'Windows-1251'
          uri=page.uri
          conv=Encoding::Converter.new('Windows-1251','UTF-8',:invalid => :replace, :undef => :replace)
          body=conv.convert(page.body)
          page = Nokogiri::HTML(body)
          page.uri=uri
        end
        page

      rescue Mechanize::ResponseCodeError => ex
        @log.debug("Exception #{ex}")

        return nil if ex.response_code=='404'

        raise ex if ex.response_code!='503'

        sleep((4-tries)*45)
        tries-=1
        @log.debug "Tries left: #{tries}"
        retry unless tries==0
      rescue Net::HTTP::Persistent::Error => ex
        @log.debug("Exception #{ex}")
        sleep((4-tries)*30)
        tries-=1
        @log.debug "Tries left: #{tries}"
        retry unless tries==0
      rescue Net::HTTPServiceUnavailable  => ex
        @log.debug("Exception #{ex}")
        sleep((4-tries)*30)
        tries-=1
        @log.debug "Tries left: #{tries}"
        retry unless tries==0
      end
    end

    ### SETTINGS METHODS

    def with_url(catalog_url)
      @catalog_urls.push catalog_url
      self
    end

    def with_product_limit(product_limit)
      if Rails.env.development?
        @product_limit=product_limit
      end

      self
    end

    def with_product_config(product_config_func=nil, product_config_class=Framework::Site::ProductConfig)
      @product_config = product_config_class.new
      @product_config = product_config_func.call(@product_config) unless product_config_func.nil?
      @product_config.with_purchase(@purchase)
      @product_config.with_logging(->(msg){@log.debug msg}, ->(msg){@log.error msg})
      self
    end

    def with_sleep(seconds)
      @sleep_time=seconds
      self
    end

    def with_category_links(getter, is_link=false)
      if is_link
        @additional_category_links.push getter
      else
        @category_links_getters << getter
      end
      self
    end

    def with_category(category_func)
      @category_func = category_func
      self
    end

    def with_pagination(next_link_getter)
      @next_link_getter = next_link_getter
      self
    end

    def delete_all
      @purchase.collections.each do |col|
        col.destroy
      end
      @purchase.product_news.each do |prod|
        prod.destroy
      end
    end

    def clean
      #Product.where(purchase_id: @purchase.id).update_all(disabled: true)
      #@purchase.collections.each do |col|
      #  col.disabled=true
      #  col.add_tag('Выкл')
      #  col.save
      #end
    end

    def disable_products
      Product.joins(:purchase).where(purchase_id:@purchase_id).where('products.downloaded_at<purchases.download_started_at').update_all(disabled:true)
      #    @purchase.collections.each do |col|
      #  col.add_tag('Выкл')
      #  col.disabled = true
      # col.save
      #end
    end

    def success(job)
      disable_products unless @stop
      if purchase.cat_model
        SpSetAiCategories.new.run(purchase)
      end

      super
      if @upload
        u=UploadPurchase.new
        u.delay(:queue => 'upload_to_sp').run(@purchase)
      end
    end

    def with_file_download_config(file_name, file_download_config_func, file_download_config_class=Framework::FileDownloadConfig)
      file_config = file_download_config_func.call(file_download_config_class.new)
      file_config.with_folder_path(@file_path)
      @file_configs[file_name] = file_config
      self
    end

  end
end