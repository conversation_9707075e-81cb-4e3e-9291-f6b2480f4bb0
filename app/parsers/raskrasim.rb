#encoding: utf-8

require 'downloader'

class Ra<PERSON><PERSON>im < Downloader
  attr_accessor :login
  attr_accessor :password

  def initialize
    @dlparams={'login'=>:text,'password'=>:password}

    @prices=Hash.new

    super
  end


  def processPage(url,cat)
    tries ||= 2

    @log.info(url)
    page = @agent.get(url)
    puts url

    name=page.search('h2')[0].text.strip

    if not page.search('ul.parameters li')[0].nil? and page.search('ul.parameters li')[0].text.include?('Размер')
      cat+=' '+page.search('ul.parameters li')[0].text.gsub('Размер:','').gsub('Раскраски','').strip
    end

    puts cat

    desc=''
    desc=page.search('.description-text')[0].text.strip if page.search('.description-text').length>0

    desc=desc.split('Похожие товары:')[0].strip if desc.include? 'Похожие товары:'
    pic=nil
    pic=page.search('.product-gallery .gallery-frame img')[0].attr('src').gsub('resize_cache/','').gsub('/368_455_1','') if page.search('.product-gallery .gallery-frame img').length>0

    price=page.search('.price-box .price').text.gsub('руб','').gsub(' ','').strip.to_f*1.05
    price=price.ceil

    pics=[]
    if not pic.nil?
      savePic(pic,name.gsub('/','_'),false)
      pics=[name.gsub('/','_')]
    end

    col=addCollection(cat,"896")
    addProduct(col,name,'',price,desc,[],pics)


  rescue Mechanize::Error => e
    puts e
    print e.backtrace.join("\n")
    retry unless (tries-=1).zero?

  end

  def processCat(url,cat)
    @log.info(url)
    page = @agent.get(url)


    page.search('#content .catalog-list .cat-item').each do |li|
      next if li.search('a.button span').text.to_s.strip=='На заказ'

      a=li.search('.item-pic a')
      processPage(a.attr('href'),cat)
    end

  end

  def process_file(f)
    book = Spreadsheet.open f

    sheet1 = book.worksheet 0
    sheet1.rows[7..-1].each_with_index do |row,i|


      name=row[1]
      price=(row[4].to_f*1.1).ceil
      @prices[name]=price.to_s
    end

  end

  def run
    #process_file(@price_file)

    page=@agent.get 'http://raskrasim.ru/'

    login_form=page.form_with(name:'form_auth')
    login_form['USER_LOGIN']=@login
    login_form['USER_PASSWORD']=@password
    page = @agent.submit(login_form, login_form.buttons[1])


    page.search("#sidebar .cat-title > a").each do |a|
      next if a.text.include? 'Бесплат'
      processCat(a.attr('href')+'?fil_by=incoming&fil_order=desc&elem_count=144',a.text.strip)
    end

    page.search("#sidebar .cat-list li > a").each do |a|
      next if a.text.include? 'Бесплат'
      processCat(a.attr('href')+'?fil_by=incoming&fil_order=desc&elem_count=144',a.text.strip)
    end

  end
  #handle_asynchronously :run

end