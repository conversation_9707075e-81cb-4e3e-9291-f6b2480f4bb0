#encoding:utf-8
require_relative '../framework/site/product_config'

module NaturaXl
  class NaturaXlProductConfig < Framework::Site::ProductConfig

    def get_page_params(product_page)
      titles = product_page.search('.title').map{|a| prepare_string a.text}
      values = product_page.search('.value').map{|a| prepare_string a.text}

      Hash[titles.zip(values)]
    end

    def get_page(link)
      begin
        @log_func.call "Get page #{link}"
        return @agent.get(link)
      rescue Mechanize::ResponseCodeError => exception
        @error_log_func.call "Has exception on #{link}, response code - #{exception.response_code}"
        if exception.response_code == '404'
          return nil
        else
          raise # Some other error, re-raise
        end
      end
    end


    def is_sale(product_page)
      not product_page.search('.newprice').first.nil?
    end

    def is_new(product_page)
      div = product_page.search('.product-label-violet').first
      return false if div.nil?

      div.text.downcase.to_s.include? 'новинка'
    end

    def get_price(product_page, selector)
      product_page.search(selector).first.text.gsub(' ', '').to_i
    end

    def parse_product(product_page, link=nil)
      return nil if product_page.nil?
      return nil if product_page.search('.product-full-title h2').first.nil?
      return nil if get_page_params(product_page)['Артикул'].blank?
      result = super(product_page, link)

      if is_sale(product_page)
        result.category = "#{result.category} - Скидки"
        result.price = get_price(product_page, '.newprice')
      else
        result.price = get_price(product_page, '.price')
      end

      result.category = "#{result.category} - Новинки" if is_new(product_page)

      result
    end

    def create_new_product(product_page, link=nil, sub_req=false)
      product = parse_product(product_page, link)
      return if product.nil?

      is_exists = Product.includes(:collection).exists?(collections: {purchase_id: @purchase.id}, art: product.articul)
      puts @purchase.id, product.articul
      @purchase.check_stop
      return if is_exists

      @color = prepare_string get_page_params(product_page)['Цвет'].split(', ').first

      add_product(product)
      get_json(product, @color).each { |key, value| @color_json[key] = value } if sub_req
      get_additional_json(product).each { |key, value| @additional[key] = value } if sub_req

      unless sub_req
        inner_products = product_page.search('.value > a:not([class])').map{|a|a.attr('href')}
        @color_json = get_json(product, @color)
        @additional = get_additional_json(product)
        current_color = @color

        inner_products.each{|href|
          p = get_page(href)
          create_new_product(p, href, true)
        }
        product.additional = @additional
        add_product_new(product, current_color, @color_json)
      end
    end

    def get_additional_json(product)
      {@color => {'product_id' => product.articul.split.last, 'description' => product.description, 'link' => product.link}}
    end

    def add_product(product)
      product.name = prepare_string(product.name + ' ' + @color)
      super(product)
    end

    def add_product_new(product, current_color, json=nil, cat_type=nil)
      puts product.name
      puts current_color
      product.name = prepare_string product.name.gsub(current_color, '')
      product.articul = product.articul.split.first
      cat_type = get_new_category_type(get_category_type(category, product.name))
      product.category = cat_type[-1]
      super(product, json, cat_type)
    end
  end

end