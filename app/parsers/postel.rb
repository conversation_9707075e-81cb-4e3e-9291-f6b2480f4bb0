#encoding: utf-8

require 'downloader'
#require 'logger'
#require 'unicode_utils/downcase'

class Postel < Downloader
  attr_accessor :login
  attr_accessor :password

  def initialize
    @dlparams={'login'=>:text,'password'=>:password}
    @piccount=0
    super
  end


  def processPage(url)
    @log.info(url)
    puts url
    page = @agent.get(url)


    art=page.search('h1').text

    cat=page.search('div.breadcrumbs li a')[1,2].map {|a| a.text}.join '-'

    page.search('table#product-attribute-specs-table tr').each do |tr|
      th=tr.search('th').text
      td=tr.search('td').text
      if th=='Общий размер'
        if td!='Нет' and td!='Не применимо'
          cat+='-'+td
        end
      end
    end
    desc=[]

    price=page.search('.product-essential span.price')[0].text.gsub(/[^0-9]/,'')

    pic=page.search('img#image').attr('src')
    puts pic

    desc=page.search('.short-description .std').text[0,254]

    @log.info art
    @log.info price
    if art==nil then return end
    if price==nil then return end

    savePic(pic,@piccount,true)

    col=addCollection(cat,'14')
    sizes=[]
    addProduct(col,art,'',price,desc,sizes,[@piccount])
    @piccount+=1

  end


  def processCat(url)
    page = @agent.get url+'?limit=all'
    page.search(".products-grid p.product-name a").each do |a|
      processPage(a.attr('href'))
    end
    rescue Mechanize::Error
  end

  def run
    if @agent==nil then before end
    page = @agent.get('http://xn--e1amhdlg6e.xn--p1ai/index.php/customer/account/login/')

    login_form=page.forms[1]
    login_form['login[username]']=@login
    login_form['login[password]']=@password
    page = @agent.submit(login_form, login_form.buttons[1])

    page = @agent.get 'http://xn--e1amhdlg6e.xn--p1ai'
    page.search("ul#nav a").each do |a|
      page2 = @agent.get a.attr('href').to_s
      dd=page2.search("//dl[@id='narrow-by-list']/dd[@class='odd']")
      if dd.count>0
        dd.search('.//a').each do |a2|
          processCat(a2.attr('href').to_s.gsub('.html','/by/tac.html'))
        end
      else
        processCat(a.attr('href').to_s.gsub('.html','/by/tac.html'))
      end

    end

  end
  #handle_asynchronously :run

end