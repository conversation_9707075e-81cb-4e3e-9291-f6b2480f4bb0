#encoding:utf-8

module UltramarineSpb
  class UltramarineSpbParser < Downloader
    attr_accessor :price_file
    attr_accessor :price_file2
    attr_accessor :price_file3
    def initialize
      @dlparams = {'price_file' => :file,'price_file2' => :file,'price_file3' => :file}
      @excel_products={}
      super
    end

    def parse_new_site_product(d)
      art=d['sku'].gsub(' .*','').gsub(/-.*/,'')
      desc = d['description'].to_s+'. '+d['sostav'].to_s
      name = d['name'].to_s
      color = d['color_name'].downcase
      pics = d['image_gallery'].to_s.split(',').map { |p| "https://ultramarine-spb.ru/img/tovary/#{p}" }

      return unless @excel_products["#{art}~#{color}"]

      return if pics.empty?

      pics=pics.map.with_index { |p,i|
        fn="#{art}~#{color.gsub('/','_')}~#{i}"
        savePic(p,fn,true)
      }

      price = @excel_products["#{art}~#{color}"][:price]
      sizes = @excel_products["#{art}~#{color}"][:sizes]

      col_name = name.gsub(art,'').strip

      col=addCollection(col_name,0)
      rrp=price.to_f*1.5
      p = addProduct(col,"#{art} #{color}" ,"#{name}, цвет #{color}",price,desc,sizes,pics, nil, 0, nil, rrp: rrp)
      unless p.weight
        p.weight = ChatGpt.new.get_product_weight(p.name)
        p.save
      end

      unless p.edited_desc
        pic_url = p.pictures[0].path.gsub('/mnt/spup/images','https://spup.primavon.ru/img512')
        p.edited_desc = ChatGpt.new.create_new_description(p.name.gsub(p.art,''), p.desc, pic_url)
        p.save
      end
    end

    def parse_new_site_cat(id)
      start = 0
      while true
        page = @agent.post('https://ultramarine-spb.ru/shop/collection_load.php',{category:id, start: start})
        res = JSON.parse(page.body)
        res.each do |product|
          parse_new_site_product(product)
        end
        break if res.count < 8
        start += 8

      end
    end
    def parse_new_site
      page = @agent.get 'https://ultramarine-spb.ru/'

      page.search('ul.dropdown a').each do |a|
        p2 = @agent.get a.attr('href')
        if /var category = '(\d+)'/ =~ p2.body
          parse_new_site_cat($1)
        end
      end
    end

    def parse_file(file_name)
      book = Spreadsheet.open file_name

      ws = book.worksheet 0
      articul = ''
      name = ''
      price = 0

      row_offset = 0
      row_offset = 1 if  ws.row(0)[0].blank?

      ws.each do |row|
        @log.debug row.to_s
        next if row[0+row_offset].nil?

        match = row[0+row_offset].match(/^(?<name>.*?)\s+арт\.\s+(?<sku>\d+S),\s+(?<color>.*?),\s+р\.\s+(?<size>\d+)$/)
        if match
          name = prepare_string match['name']
          articul = match['sku']
          color = prepare_string(match['color'].gsub('х/б',''))
          size = match['size']
          price = row[2+row_offset].to_s.gsub(' ', '').to_i if row[2+row_offset]

          if @excel_products["#{articul}~#{color}"]
            @excel_products["#{articul}~#{color}"][:sizes]<<size
          else
            @excel_products["#{articul}~#{color}"]={art:articul,name:name,price:price,sizes:[size],color:color}
          end

          next
        end

        match = row[0+row_offset].match(/м(од)?\s*\.\s*(?<articul>[A-Z\d\-]+)/)

        match = match['articul'] unless match.nil?

        unless match.blank?
          name = prepare_string row[0+row_offset].split.take_while{|s| not (s.include? 'м.' or s.include? 'мод.' or s.include? 'м .' or s.include? 'мод .')}.join ' '
          price = row[2+row_offset].to_s.gsub(' ', '').to_i
          articul = match
          next
        end

        color = prepare_string row[0+row_offset].split(',').take_while{|s| not (s.include?('размер') || s.include?('р.'))}.join(', ')

        if row[0+row_offset].include? 'размер'
          size = prepare_string row[0+row_offset].split(',')[-1].gsub('размер', '')
        elsif /р\. *(\d+)/=~row[0+row_offset]
          size = $1
        else
          size = ''
        end

        size.gsub!(/^[, ]+/, '')
        size.gsub!(/[, ]+$/, '')

        if @excel_products["#{articul}~#{color}"]
          @excel_products["#{articul}~#{color}"][:sizes]<<size
        else
          @excel_products["#{articul}~#{color}"]={art:articul,name:name,price:price,sizes:[size],color:color}
        end

        @log.debug "#{articul} #{name} #{color} #{size} #{price}"
      end
    end

   def parse_old_site
      page = @agent.get 'https://old.ultramarine-spb.ru/Zima-2020-2021/Palto-240W/chyorno-sinij/'
      if /all_models_data=(.*?)mylog/m =~ page.body
        json = $1
        json.strip!
        json.chomp!(';')

        res = JSON.parse(json)

        site_products = {}
        res.each do |l|
          l = l[1]
          art = l[8]
          art = "D#{$1}" if /^(\d+)D$/ =~ art
          color = l[10]
          pic_urls = l[25..28]
          desc = []
          desc << "Ткань: #{l[31]}" unless l[31].blank?
          desc << "Состав: #{l[33]}" unless l[33].blank?
          desc << "Утеплитель: #{l[35]}" unless l[35].blank?
          desc = desc.join '. '

          desc.gsub!('<br>', "\n")

          next if @excel_products.values.find { |p| p[:art] == art }.nil?

          pics = []
          pic_urls.each_with_index { |url, i|
            fn = "#{art}~#{color.gsub('/', '_')}~#{i}"

            pics << savePic("https://old.ultramarine-spb.ru/936/#{url}.jpg", fn, true)
          }

          site_products["#{art}~#{color}"] = {
            art: art,
            color: color,
            pics: pics,
            desc: desc,
          }
        end

        @excel_products.each do |art_color, ep|
          d = nil
          other_color = ''
          if site_products[art_color]
            d = site_products[art_color]
            pics = d[:pics]
          else
            d = site_products.values.select { |p| p[:art] == ep[:art] }
            pics = d.map { |p| p[:pics] }.flatten
            d = d.first
            other_color = 'Цвет на картинке может не соотвествовать цвету изделия. '
          end

          if d
            col = addCollection(ep[:name], 0)
            rrp = ep[:price].to_f * 1.5
            p = addProduct(col, "#{d[:art]} #{ep[:color]}", "#{ep[:name]}, цвет #{ep[:color]}", ep[:price], "#{other_color}#{d[:desc]}", ep[:sizes], pics, nil, 0, nil, rrp: rrp)
            unless p.weight
              p.weight = ChatGpt.new.get_product_weight(p.name)
              p.save
            end

            unless p.edited_desc
              pic_url = p.pictures[0].path.gsub('/mnt/spup/images','https://spup.primavon.ru/img512')
              p.edited_desc = ChatGpt.new.create_new_description(p.name.gsub(p.art,''), p.desc, pic_url)
              p.save
            end


          end

        end
      end
    end

    def run
      parse_file @price_file
      parse_file @price_file2 if @price_file2
      parse_file @price_file3 if @price_file3

      ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest', 'Accept' => 'application/json, text/javascript, */*'}


      #      site_products=[]
      #     cats=[1,8,5,4]
      #      cats.each do |cat|
      #        page=0
      #        while true
      #          p=@agent.post 'https://ultramarine-spb.ru/shop/collection_load.php',{category:cat,page:page},ajax_headers
      #          d=JSON.parse(p.body)
      #          page+=8
      #          break if d.count==0
      #          site_products+=d
      #          site_products+=d
      #        end
      #      end

      parse_new_site
      parse_old_site

    end


  end
end