module Marita
  class MaritaParser < Downloader
    @dlparams={'login'=>:text,'password'=>:password}

    def initialize
      @dlparams={'login'=>:text,'password'=>:password}
      super
    end

    def process_page(href)
      puts href

      begin
        page=@agent.get href
      rescue
        return
      end

      cat=page.search('#bx_breadcrumb_1 span[itemprop=title]').text

      art2=page.search('.bx-breadcrumb-item span').last.text.strip

      art=URI::Parser.new.unescape(href.split('/')[-2..-1].join('_').gsub(/\.html$/,''))
      options={}
      page.search('.catalog_detail_cont > p').each {|p|
        title=p.search('>span').first.text.strip
        value=p.text.gsub(title,'').strip
        title.gsub!(/:$/,'')
        options[title]=value
      }

      name=options['Артикул']
      desc=options['Описание']
      desc='' if desc.nil?
      desc+=", материал: #{options['Материал']}" if options['Материал']
      desc+=", цвет: #{options['Цвет']}" if options['Цвет']

      desc.gsub!(/^, /,'')

      prices={}

      page.search('table.catalog_tables tr.catalog_tables_tr2').first.search('td')[1..-1].each {|td|
        price=td.text
        size=td.attr('id').to_s.gsub(/^price/,'')
        prices[price]=[] unless prices[price]
        prices[price] << size
      }

      pic_urls=page.search('img.etalage_source_image').map {|img| img.attr('src').to_s}

      pics=[]
      pic_urls.each_with_index {|url,i|
        fn="#{art}~#{i}"
        pics<<savePic(url,fn,false)
      }

      cat_type='1101'

      col=addCollection(cat,cat_type)
      prices.each {|price,sizes|
        addProduct(col,art+' '+art2,name,price,desc,sizes,pics)
      }

    end


    def process_cat(href)
      puts "Cat #{href}"
      page=@agent.get href

      page.search('ul.catalog li.catalog_elem a.catalog_elem_link').each {|a|
        process_page(a.attr('href').to_s)
      }

    end

    def run
      if @agent==nil then before end

      page=@agent.get 'http://marita-opt.ru/auth/'
      login_form=page.form_with(name:'form_auth')
      login_form['USER_LOGIN']=@login
      login_form['USER_PASSWORD']=@password
      page = @agent.submit(login_form, login_form.buttons[0])

      page.search('ul.head_menu_cont ul a.head_menu__link').each {|a|
        href=a.attr('href').to_s
        process_cat(href+'?perpage=all')
      }


    end
  end
end

