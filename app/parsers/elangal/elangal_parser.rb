# frozen_string_literal: true
module Elangal
  class ElangalParser < Downloader

    def initialize
      @dlparams = {}
      super
    end

    def pp_object
      ElanPp.new
    end
    def login
      page = @agent.get 'https://elangal.ru/login/?login=yes'
      login_form = page.form_with(:name => 'form_auth')
      login_form['USER_LOGIN'] = 'moral'
      login_form['USER_PASSWORD'] = '27021979'
      @agent.submit(login_form, login_form.buttons[0])
    end

    def process_page(href, cat)
      @log.info href

      #href = 'https://elangal.ru/catalog/servirovka-stola/blinnitsy/blinnitsa-23-23-9-5-sm-lavanda-new-bone-china.html'
      return if @visited.include?(href)
      @visited << href

      page = @agent.get href
      name = page.at('.item_title h1').text.strip
      code = page.at('span.cml2_article b').text.strip
      desc = page.at('.item_description').text.strip
      price = page.at('.item_price span.price_value').text.strip.gsub(' ','').to_f
      #price = (price*1.05).ceil(-1)

      full_price = page.at('.item_price span.old-price-value').text.strip.gsub(' ','').to_f if page.at('.item_price span.old-price-value')

      price = (full_price*0.9).round(-1).ceil if full_price.to_f*0.9 > price

      rrp = (price.to_f*1.5).ceil(-1)

      pic_urls = page.search('#gallery a[data-zoom-image]').map { |a| a.attr('data-zoom-image') }

      if cat.blank?
        cat = page.search('.bx-breadcrumb-item span')[2].text.strip
      end

      pics = []

      pic_urls.each_with_index do |pic_url, i|
        pics << savePic(pic_url, "#{code}~#{i}", true, true, false, :active, show_order: i)
      end

      col = addCollection(cat, 0)

      brand = 'Элан галерея'
      prod = addProduct(col, code, name, price, desc, [], pics, nil, 0, nil, rrp: rrp, source: href, brand_name: brand)
      #prod = addProduct(col, code, name)

    end
    def process_cat(href, cat)
      @log.info href
      #return

      page = @agent.get href
      page.search('a.catalog-item-name').each do |a|
        process_page(a.attr('href'), cat)
        sleep(0.5+rand(0.7))
      end
      sleep(0.5+rand(0.7))

    end

    def update_dimensions

      gpt = ChatGpt.new
      lines = []

      Product.where(purchase_id: @purchase_id, disabled: false, weight: nil).each do |p|
        lines << gpt.get_product_dimensions(p.name,description: nil, batch_id: "prod_dims_#{p.id}")
        break if Rails.env.development? # limit for development
      end

      fn=Rails.root.join('files', "chatgpt_dims_#{@purchase_id}.jsonl").to_s
      File.open(fn, 'w') { |f| f.write(lines.join("\n")) }
      r = gpt.upload_file(fn, 'batch')
      puts r
      r = gpt.create_batch(r['id'])
      puts r
      ChatGptBatch.create(batch_id: r['id'], status: 'validating', purchase_id: purchase_id, filename: fn, expires_at: r['expires_at'], batch_type: self.class.name )

    end

    def process_result(batch)
      gpt = ChatGpt.new

      content = gpt.client.files.content(id: batch['output_file_id'])
      # puts content[0..100]

      content.each do |res|
        if res['custom_id'] && res['custom_id'].start_with?('prod_dims_')
          product_id = res['custom_id'].split('_')[2].to_i
          product = Product.find(product_id)

          d = JSON.parse(res.dig('response', 'body', "choices", 0, "message", 'content'))
          product.weight = (d['weight'].to_i*1.1).round(-1)+30
          product.box_width = (d['width'].to_i*1.1).ceil(-1)
          product.box_height = (d['height'].to_i*1.1).ceil(-1)
          product.box_depth = (d['depth'].to_i*1.1).ceil(-1)


          product.save
        end
      end
    end

    def run
      before if @agent == nil
      page = login

      @visited = Set.new

      page.search('ul.left-menu > li').each do |li|
        cat1 = li.at('a').text.strip
        if li.at(' > ul')
          li.search('ul > li > a').each do |a|
            cat2 = a.text
            unless cat1.blank?
              cat = "#{cat1} - #{cat2}"
            else
              cat = cat2
            end
            process_cat(a.attr('href')+'?SHOWALL_1=1', cat)
          end
        else
          a = li.at('a')
          process_cat(a.attr('href')+'?SHOWALL_1=1', cat1)
        end
      end

      update_dimensions
      ChatGptNameBatch.new.run(@purchase_id)
    end
  end
end
