#encoding:utf-8
require_relative '../framework/site/product_config'
module UsiPusi
  class UsiPusiProductConfig < Framework::Site::ProductConfig
    def initialize_old
      @site_products = []
      @articul_map = {'02251'=>'02151', '06321'=>'06231', '11247'=>'11172', '11315'=>'11326'}
      super
    end

    def create_new_product(product_page, link=nil)
      puts 'create new product'
      product = parse_product(product_page, link)

      if product.nil?
        @error_log_func.call "Product is nil for link #{link} and element #{product_page}"
        return
      end
      @purchase.check_stop

      @log_func.call(product.articul)
      excel_products = @get_excel_products.call.find_all{|p|p.articul == product.articul}

      return if excel_products.length == 0
      return if product_page.uri.to_s.include? '&path=67_81&product_id=150'
      excel_products.each do |excel_product|
        excel_product.images = product.images.map{|i|i}
      end
    end

    def with_agent(agent)
      @agent = agent
      initialize_image_config
    end

    def with_excel_products(get_excel_products)
      @get_excel_products = get_excel_products
      self
    end
  end
end
