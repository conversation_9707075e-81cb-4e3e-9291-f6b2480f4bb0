#encoding: utf-8
module Acoola
  class AcoolaParser < Downloader
    include InvoiceLala
    @dlparams = {'login' => :text, 'password' => :password}

    def initialize
      @dlparams = {'login' => :text, 'password' => :password}

      @prices = {}
      @sizes = {}
      super
    end

    def get_new_category_type(cat)

      cat.each do |c|
        return ['Детям и подросткам','Одежда','Для девочек'] if c.include? 'девочек'
        return ['Детям и подросткам','Одежда','Для мальчиков'] if c.include? 'мальчиков'
      end

    end


    def process_page(url,cat1)
      @log.info(url)
      page = @agent.get(url)
      @purchase.message="Загрузка #{url}"
      @purchase.save

      name=page.search('h1#page_main_header').text.gsub(/\[.*/,'').strip
      props=Hash.new

      if page.search('.properties-list').count>0
        page.search('.properties-list .properties-list-row').each do |div|
          k=div.search('.item-add-field-name')[0].text.downcase
          v=div.search('.item-add-field-action')[0].text.downcase
          props[k]=v
        end
      end

      art=props.delete('артикул')
      cat2=props.delete('вид изделия')
      desc=props.map {|k,v| "#{k}: #{v}"}.join ', '

      desc=props.map {|k,v| "#{k}: #{v}"}.join '-'
      form=page.search('form[id^=add2basket_form_]').first
      pid=form.attr('id').to_s.gsub('add2basket_form_','')
      art+=" #{pid}"

      sizes=page.search("input[class^=basket_opt_#{pid}_]").map{|s| s.attr('value').to_s}

      pics=[]

      return if page.search('img[itemprop=image]').count==0

      page.search('a[rel=gallery]').each {|a| pics<<'https://a-coola.ru'+a.attr('href').to_s}

      pics2=[]
      pics.each_with_index do |url,i|
        fname="#{pid}~#{i}"
        savePic(url,fname,true) { |pic| process_img(pic) }
        pics2<<fname
      end

      price=page.search('span[itemprop=price]').text.to_f

      color_data={}

      s2=Hash.new
      sizes.each do |s|
        s2[s]={price:(price.to_f*1.1).round,stock_price:price,retail_price:(price.to_f*2).round}
      end
      color_data['']={sizes:s2,pics:pics2}

      product = addProductNew(nil, "#{cat1}-#{cat2}", get_new_category_type([cat1,cat2]), art, name, desc, color_data, nil)

      product.composition=desc
      product.save

    end

    def process_good_list(page,cat)
      page.search('.twr h2.twr-name a').each {|a|
        href = a.attr('href').to_s

        process_page(href,cat)
      }

    end

    def process_cat(href,cat)
      #puts "Cat #{href}"
      page = @agent.get href

      process_good_list(page,cat)

      page.search('#pages a').each do |a|
        page2 = @agent.get a.attr('href').to_s
        process_good_list(page2,cat)
      end

    end

    def process_img(pic)
      rows = pic.rows
      cols = pic.columns

      remove_rows=0
      remove_rows_bottom=0
      scanning_top=true
      rows.times { |y|
        pixels = pic.get_pixels(0, y, cols, 1)
        avg=0
        pixels.each_with_index { |p|
          avg+=(p.red+p.green+p.blue)/257
        }
        avg/=cols*3
        #puts "Avg line color: #{avg}"
        remove_rows=y if scanning_top
        scanning_top=false if avg<240

        remove_rows_bottom=y if avg<240
      }
      remove_rows-=50
      remove_rows_bottom+=50 if remove_rows_bottom<rows
      remove_rows=0 if remove_rows<0

      if remove_rows>0
        #part=pic.get_pixels(0,remove_rows,cols,rows-remove_rows)
        #pic.store_pixels(0,0,cols,rows-remove_rows,part)
        pic.crop!(0, remove_rows, cols, remove_rows_bottom-remove_rows, true)
      end

    end


    def run
      if @agent == nil then
        before
      end

      page = @agent.get('https://a-coola.ru/?show=profile&act=login')

      login_form=page.form_with(:action=>'/?show=profile&act=login')
      login_form['email']=@login
      login_form['password']=@password
      page = @agent.submit(login_form, login_form.buttons[0])

      page.search('nav#main-menu > ul > li > a').each {|a|
        process_cat(a.attr('href').to_s,a.text.to_s)
      }

    end

  end
end
