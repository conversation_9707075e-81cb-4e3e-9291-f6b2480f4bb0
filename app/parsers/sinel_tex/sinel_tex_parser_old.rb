#encoding:utf-8

require_relative '../framework/parser'
require_relative 'sinel_tex_product_config'

module SinelTex
  class SinelTexParserOld < Framework::Parser
    include InvoiceSinel

    def initialize_settings
      with_url('https://www.sinel-tex.ru/odezhda-dlya-doma/')
          .with_category_links(->(catalog_page) {
            result = catalog_page.search('.menu a').map { |a| a.attr('href').to_s }
            result[-2], result[0] = result[0], result[-2]
            result
          })
          .with_pagination(->(category_page) {
            link = category_page.search('.new_page_navigation > a').find { |a| a.text == '>' }
            return nil if link.nil?
            link.attr('href').to_s
          })
          .with_product_config(->(pc) {
            pc.with_product_selector('.link_kartochka')
                .with_name(->(product_page) {
                  name = prepare_string product_page.search('h1').first.text
                  color = product_page.search('.cl-color').first
                  return "#{name} #{color.text}" unless color.nil?
                  name
                })
                .with_articul(->(product_page) {
                  articul = prepare_string product_page.search('#header_num').first.text.gsub(prepare_string(product_page.search('h1').first.text),'').strip
                  product_id = product_page.search('#tovar_form > input[type=hidden]').first.attr('value').to_s

                  "#{articul} #{product_id}"
                })
                .with_category(->(page) {
                  cat=prepare_string page.search('.breadcrumbs > span > a > span').last.text
                  cat="#{cat} - скидки" if page.search('img.skid_icon').count>0
                  cat
                })
                .with_price(->(product_page){
                  #cat=prepare_string product_page.search('.breadcrumbs > span > a > span').last.text
                  #return product_page.search('.price span.old_price').first.text.gsub(/[^\d\.,]/, '').gsub(',','.').to_f.round(2) if product_page.search('.price span.old_price').count>0 and cat!='Распродажа %'

                  product_page.search('meta[itemprop=price]').first.attr('content').to_s.to_f
                })
              .with_brand(->(_){
                'СИНЕЛЬ'
              })
              .with_rrp(->(product_page){
                #cat=prepare_string product_page.search('.breadcrumbs > span > a > span').last.text
                #return product_page.search('.price span.old_price').first.text.gsub(/[^\d\.,]/, '').gsub(',','.').to_f.round(2) if product_page.search('.price span.old_price').count>0 and cat!='Распродажа %'

                (product_page.search('meta[itemprop=price]').first.attr('content').to_s.to_f*1.6).to_i
              })
                .with_images(->(product_page) {
                  images = product_page.search('.detail_imimig a').map { |a| a.attr('href').to_s }

                  images.concat(product_page.search('.column-left > a').map { |a| a.attr('href').to_s }).uniq
                })
                .with_image_config(->(ic) {
                  ic.with_default_url('https://www.sinel-tex.ru')
                })
                .with_sizes(->(product_page) {
                  product_page.search('.size:not(.inactive) > .size-num').map { |s| prepare_string s.text }
                })
                .with_description(->(product_page) {
                  prepare_string product_page.search('.dotted-bottom .info-header:last-of-type').first.text
                })
                .with_category_type('24')
                .with_category_type('15', 'блузы')
                .with_category_type('61', 'большие')
                .with_category_type('17', 'брюки ')
                .with_category_type('3012', 'комплекты')
                .with_category_type('3013', 'сорочки')
                .with_category_type('1104', 'сарафаны')
                .with_category_type('307', 'туники')
                .with_category_type('3011', 'халаты')
          }, SinelTex::SinelTexProductConfig)
    end
  end
end