module Naturevector
  class NaturevectorParser < Downloader

    def initialize
      super
      @dlparams = {}

    end

    def chatgpt_request(msg)
      client = OpenAI::Client.new(access_token: "***************************************************")

      retry_count = 2
      while retry_count >= 0
        response = client.chat(
          parameters: {
            model: "gpt-4.1", # Required.
            messages: [{ role: "user", content: msg }], # Required.
            temperature: 0.1,
          })
        puts response

        if response['error']
          retry_count -= 1
        else
          return response.dig("choices", 0, "message", "content")
        end
      end
      nil
    end

    def get_clip_image_desc(file)
      agent = Mechanize.new
      agent.log = Logger.new('log/ttt.log')

      if Rails.env.development?
        url = "http://127.0.0.1:5566/image"
      else
        url = "http://**************:5566/image"
      end

      res = agent.post(url, { "image" => File.open(file, 'rb') })
      res = res.body.downcase.split(' ').reject { |s| s.blank? || /[^a-z0-9,.]/ =~ s }.join(' ').gsub(' ,', ',').gsub(' .', '.').strip
      res.gsub(/ins?pired.*/, '').gsub(/trending.*/, '')
    rescue
      nil
    end

    def guess_nail_color(name, color, image = nil)
      colors = %w(белый черный красный зеленый синий желтый розовый серый коричневый оранжевый фиолетовый голубой бежевый золотой серебряный)

      if color
        c = color.downcase.gsub(/[^а-я]/, '').strip
        return c if colors.include?(c)
      end

      ret = Cache.get_value("color_chatgpt", name)
      return ret if ret

      req_name = name
      if image
        t = get_clip_image_desc(image)
        req_name = t if t
      end

      msg = "Список цветов: #{colors.join(',')}. Ответ: строго одно слово из списка выше, не добавляй лишнего текста. Какой цвет у лака #{req_name}"

      msg = "#{msg}, #{color}" if color

      response = chatgpt_request(msg)

      res = response.downcase.gsub(/[^а-я]/, '').strip
      res = "другой" if !colors.include?(res)

      Cache.set_value("color_chatgpt", name, res)
      res
    end

    def get_product_details(product_page)
      titles = product_page.search('.catalog-detail-property .name').map { |s| prepare_string s.text }
      values = product_page.search('.catalog-detail-property .val').map { |s| prepare_string s.text }

      Hash[titles.zip(values)]
    end

    def process_page(href)
      #href = '/catalog/product/likato_krem_vosstanavlivayushchiy_uprugost_kozhi_s_kollagenom_i_ferulovoy_kislotoy_ferulic_acid_and_/' if Rails.env.development?

      @log.info href

      #      href = 'https://kristaller.pro/catalog/product/krem-dlya-litsa-uvlazhnyayushchiy-s-ekstraktom-aloe-farmstay-visible-difference-fresh-cream-aloe/' if Rails.env.development?
      tries = 3

      begin
        page = @agent.get(href)
        page.body.force_encoding 'utf-8'
      rescue Mechanize::Error => e
        sleep sleep((3 - tries) * 15) + 1
        retry unless (tries -= 1).zero?
      end

      return unless page
      return unless page.at('main picture img')

      name = page.at('h1.uk-h2').text.strip
      desc = ''
      desc = page.at('div.tm-source-woo-description').text.strip if page.at('div.tm-source-woo-description')
      cat = page.search('ul.uk-breadcrumb li a span')[1].text.strip
      price = page.at('span.woocommerce-Price-amount bdi').text.to_i+1
      price = (price.to_f*1.05).ceil
      rrp = (price * 1.4).round
      art = page.at('span.sku').text.strip

      brand='Компас здоровья'
      brand = page.at('span:contains("Бренд") a').text.strip if page.at('span:contains("Бренд") a')

      col = addCollection(cat,0)

      pictures = []
      picture_url = page.at('main picture img').attr('src')
      pictures << savePic(picture_url, "#{art.gsub('#', '_')}", true, true, false, :active, nil)

      addProduct(col, art, name, price, desc, [], pictures, nil, 0, nil, rrp: rrp, brand_name: brand, source: href)


    end

    def cell_in_sqref?(target_row, target_col, sqref)
      return false if sqref.nil? || sqref.empty?

      # sqref может содержать несколько диапазонов, разделенных пробелами
      ranges = sqref.to_s.split(' ')

      ranges.each do |range_str|
        # Диапазон может быть одной ячейкой ("A1") или областью ("A1:B5")
        cells = range_str.split(':')

        begin
          # Получаем координаты верхнего левого угла диапазона
          start_row, start_col = RubyXL::Reference.ref2ind(cells[0])

          # Если диапазон состоит из одной ячейки
          if cells.length == 1
            return true if target_row == start_row && target_col == start_col
            # Если диапазон - это область
          elsif cells.length == 2
            # Получаем координаты нижнего правого угла диапазона
            end_row, end_col = RubyXL::Reference.ref2ind(cells[1])

            # Проверяем вхождение (учитывая, что start/end могут быть перепутаны)
            min_row, max_row = [start_row, end_row].minmax
            min_col, max_col = [start_col, end_col].minmax

            if target_row >= min_row && target_row <= max_row &&
              target_col >= min_col && target_col <= max_col
              return true
            end
          end
        rescue ArgumentError => e
          # Не удалось разобрать ссылку (маловероятно для корректных sqref, но на всякий случай)
          puts "Предупреждение: Не удалось разобрать часть диапазона '#{range_str}' в sqref: #{e.message}"
          next # Переходим к следующему диапазону в sqref
        end
      end

      # Если ни один из диапазонов в sqref не подошел
      return false
    end

    def find_validation_ranges(sheet,str)
      return nil unless sheet.data_validations
      sheet.data_validations.each do |val|
        return val.sqref if val.error.to_s.include? str
      end
    end
    def read_file
      price_url = get_price_url
      file = @agent.get price_url
      workbook = RubyXL::Parser.parse_buffer(file.body)
      sheet = workbook.worksheets[0]

      disabled_ranges = find_validation_ranges(sheet,'отсутствует')
      full_box_ranges = find_validation_ranges(sheet,' кратно ЦЕЛЫМ')
      half_box_ranges = find_validation_ranges(sheet,' кратно 1\2')

      in_data = false
      sheet.sheet_data.rows.each_with_index do |row, index|
        next if row.nil? || row[6].nil? || row[6].value.blank?
        in_data = true if row[6].value == 'от 15 тыс'
        next unless in_data

        next unless row[1].value

        art = row[1].value.to_s

        next if disabled_ranges && cell_in_sqref?(index, 5, disabled_ranges)

        brand='Компас здоровья'

        barcode = row[2].value.to_s
        name = row[3].value
        in_box = row[4].value.to_i
        price = row[8].value
        brand = row[9].value unless row[9].value.blank?
        category = row[10].value
        comment = row[14].value
        weight = row[36].value*1000 if row[36].value
        pic_url = row[40].value
        desc = row[18].value

        pack_size = nil

        if comment && /квант (\d+)/=~comment.downcase
          pack_size = $1.to_i
        else
          pack_size = in_box if comment&.include?('квант')
          pack_size = in_box/2 if half_box_ranges && cell_in_sqref?(index, 5, half_box_ranges)
          pack_size = in_box if full_box_ranges && cell_in_sqref?(index, 5, full_box_ranges)
          pack_size = in_box if comment&.include?('коробками')
        end

        if pack_size
          price *= pack_size
          weight *= pack_size
          name = "#{name} (Упаковка #{pack_size} шт)"
          desc = "Цена за упаковку. #{desc}"
        end

        col = addCollection(category,0)

        pictures = []
        # pictures << savePic(pic_url, "#{art.gsub('#', '_')}", true, true, false, :active, nil)

        price = (price.to_f*1.05).ceil
        rrp = (price * 1.4).round

        addProduct(col, art, name, price, desc, [], pictures, nil, 0, nil, rrp: rrp, brand_name: brand, source: "#{price_url}##{index}", barcode: barcode, weight: weight)

        next
      end
    end

    def get_price_url
      page = @agent.get 'https://naturevector.ru/zakaz/'
      page.at('div#download_price a').attr('href')
    end
    def run
      if @agent == nil then
        before
      end

      read_file
      return

      p = 1
      while true do
        page = @agent.get("https://naturevector.ru/zakaz/?4993_orderby=option_3&4993_filtered=true&4993_paged=#{p}")
        p += 1
        trs = page.search('tr.wcpt-product-type-simple')
        break if trs.size == 0

        trs.each do |tr|
          a = tr.at('a')
          process_page a.attr('href')
        end
      end

      DeepseekWeight.new.run(@purchase.id)
    end

    def pp_object
      raise
    end
  end
end