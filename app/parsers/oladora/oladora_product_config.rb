#encoding:utf-8
require_relative '../framework/site/product_config'
module Oladora
  class OladoraProductConfig < Framework::Site::ProductConfig

    def get_page(link)
      begin
        sleep(rand(0.9) + 0.8)
        @agent.get(link)
      rescue Mechanize::ResponseCodeError => exception
        if exception.response_code == '502'
          sleep(1.minute)
          @agent.get(link)
        else
          raise # Some other error, re-raise
        end
      end
    end

    def get_material(product_page)
      cells = product_page.search('#product-features td').map { |td| prepare_string td.text }
                  .each_slice(2)
                  .drop(1)

      Hash[cells]['Материал']
    end

    def get_colors(product_page)
      match = product_page.body.match(/features: ({.+?})}/)
      if match.nil?
        return {
            '' => {
                'price' => product_page.search('[itemprop=offers] [itemprop=price]').first.attr('content').to_i
            }
        }
      end
      color_json = match.captures[0].gsub(';', '')
      color_json = JSON.parse "#{color_json}}"
      color_json.each do |key, json|
        puts key
        parts = key.split(':')
        # label = product_page.search('.at-stylize-label').find { |l| l.search("") }
        if parts[0] == '3'
          span = product_page.search(".at-stylize-input[value='#{parts[1]}'] + span").first
          json['name'] = prepare_string span.attr('title') unless span.nil?
          next
        elsif parts[0] == '17'
          option = product_page.search(".sku-feature > option[value='#{parts[1]}']").first
          option = product_page.search(".sku-feature[value='#{parts[1]}']+span").first if option.nil?
          json['name'] = prepare_string option.text
        end
      end
      color_json = color_json.delete_if {|k,v| not v.key? 'name'}
      puts color_json
      color_json
    end

    def get_common_images(product_page, colors)
      image_ids = colors.map { |k, json| json['image_id'] }

      all_image_ids = product_page.search('.image > a').find_all { |a| not a.attr('id').to_s.nil? }.map { |a| a.attr('id').to_s.split('-')[-1].to_i }

      result = all_image_ids - image_ids
      result.delete_if { |r| r == 0 }

      puts "#{image_ids}"
      puts "#{all_image_ids}"
      puts "#{result}"

      result
    end

    def create_new_product(product_page, link=nil)
      puts link
      common = parse_product(product_page, link)

      material = get_material(product_page)
      material = '' if not material.blank? and material.downcase.include? 'угря'
      common.category = "#{common.category}, #{material}" unless material.blank?
      colors = get_colors(product_page)
      common_images = get_common_images(product_page, colors)
      json = {}
      colors.each do |key, color|
        product = common.clone
        puts key
        @get_images = ->(product_page) {
          puts 'image_ids'
          result = common_images.map { |i| i }.to_a
          result.insert(0, color['image_id']) unless color['image_id'] == 0 or not color.key?('image_id')
          return product_page.search('.image > a').map{|a| a.attr('href').to_s}.uniq if result.length == 0

          result.map { |id|
            puts id
            product_page.search(".image > a#product-image-#{id}").first.attr('href').to_s }
        }

        product.articul = "#{product.articul} #{key.split(':').last}"
        product.name = prepare_string "#{product.name} #{color['name']}"

        product.price = color['price']
        next if product.price == 0
        product.images = save_images(product_page, "#{product.articul}~#{color['name']}")

        add_product(product)
        json.merge!(get_json(product, color['name']))
      end
      @get_images = ->(p){[]}
      add_product_new(common, json)
    end
  end
end