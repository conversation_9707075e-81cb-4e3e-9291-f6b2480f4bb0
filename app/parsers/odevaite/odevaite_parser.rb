#encoding:utf-8
require_relative '../framework/parser'
require_relative '../framework/sizeable_parsed_product'
require 'spreadsheet'

module Odevaite
  class OdevaiteParser < Framework::Parser
    attr_accessor :price_file
    attr_accessor :zip_file

    def initialize
      # @dlparams = {'price_file' => :file}
      @dlparams = {'price_file' => :file, 'zip_file' => :file}
      super
    end

    def get_description(product_page)
      inner_html = product_page.search('.col-xs-6.col-sm-12.no-float-sm').first.inner_html
      prepare_string inner_html.gsub(/(?i)<[^>]*>/, ' ')

      result = inner_html.to_s[4..-1].gsub('<h3>', '. ').gsub('</h3>', ': ').gsub(/\. Артикул: (?<articul>\d+(-\d+)?)\./, '')
        .gsub('. Артикул:', '')

      prepare_string result
    end

    def initialize_settings
      @excel_products = get_products
      with_url('http://www.odevaite.ru')
          .with_category_links('.col-md-3>menu>li>a')
          .with_pagination('.pagination > li:last-of-type:not(.active) > a')
          .with_product_config(->(pc) {
            pc.with_product_selector('.bj-product-card__img__shutter')
                .with_category(->(product_page) {
                  product_page.search('.breadcrumb > li > a').drop(1).map { |a| a.text }.join ', '
                })
                .with_articul(->(product_page) {
                  inner_html = product_page.search('.col-xs-6.col-sm-12.no-float-sm').first.inner_html
                  prepare_string inner_html.gsub(/(?i)<[^>]*>/, ' ')
                }, /Артикул (?<articul>\d+(-\d+)?)/)
                .with_name(->(product_page) {
                  product_page.search('h1').first.text
                })
                .with_price(->(product_page) {
                  text = product_page.search('.item_current_price').find { |s| not s.text.blank? }
                  return 0 if text.nil?
                  text.text.gsub(' ', '').match(/\d+/).to_s.to_f
                })
                .with_description(->(product_page) {
                  get_description(product_page)
                })
                .with_image_config(->(ic) {
                  ic.with_default_url('http://www.odevaite.ru')
                })
                .with_category_type('850')
                .with_category_type('2426', 'детский', 'ассортимент')
                .with_category_type('870', 'мужской', 'ассортимент')
                .with_category_type('850', 'женский', 'ассортимент')
                .with_validator(Framework::SizeableProductValidator)
                .with_excel_products(->(){@excel_products})
          }, Odevaite::OdevaiteProductConfig)
    end

    def run
      super
      @excel_products.find_all{|p|not p.is_added}.each do |product|
        @product_config.add_product product
        @product_config.add_product_new product
      end
    end

    def get_products
      excel_path = "#{@picture_path}excel"
      FileUtils.mkpath(excel_path) unless File.exist? excel_path
      excel_products = parse_excel
      puts "zip #{@zip_file}. excel length #{excel_products.length}"
      zip_file = Zip::File.open(@zip_file)
      excel_products.each { |product|
        saved_images = []
        entries = zip_file.find_all{|entry|entry.name.include?(product.articul)}
        puts "Articul:#{product.articul}. Entries length:#{entries.length}"
        entries.each_with_index do |entry, index|
          path = "#{excel_path}/#{product.articul}~#{index}.jpg"
          puts path
          entry.extract(path){true}
          hash = Digest::MD5.hexdigest(File.read(path))
          size = File.size(path)
          saved_images << Framework::Image.new(path, size, hash)
        end
        product.images = saved_images
      }
      excel_products
    end

    def parse_excel
      result = []

      book = Spreadsheet.open @price_file

      ws = book.worksheet 0
      category = ''
      ws.each do |row|
        category = prepare_string(row[0].to_s) if row[1].nil? and row[3].nil? and not row[0].nil? and row[0].to_s.match(/\d/).nil?
        next if row[1].nil? or row[1].to_s.match(/\d/).nil?
        articul = row[1].to_s.match(/(\d+-\d+)/)
        name = prepare_string row[0]
        if articul.nil?
          result[-1].sizes << prepare_string(row[1].to_i.to_s)
          result[-1].price = row[3].to_f if result[-1].price == 0
        else
          result << Framework::SizeableParsedProduct.new(category, articul.captures[0].to_s, [], name, '', 0, [])
        end
      end
      result
    end
  end
end
