#encoding:utf-8
require_relative '../framework/site/product_config'
require_relative '../framework/image'
require 'zip'

module Odevaite
  class OdevaiteProductConfig < Framework::Site::ProductConfig
    def get_script_json(product_page)
      script = product_page.search('.bj-page-content > script').first.text
      script = script.match(/JCCatalogElement\((.+)\);/).captures[0].to_s.gsub('\'', '"')
      puts script
      JSON.parse(script)
    end

    def create_new_product(product_page, link=nil)
      @get_sizes = ->(product_page){[]}
      product = parse_product(product_page, link)
      json = get_script_json(product_page)

      if json['OFFERS'].nil?
        json = json['PRODUCT']
        pics = json['SLIDER'].map{|p|p['SRC']}
        saved_images = []
        pics.each_with_index { |url, i| saved_images <<  @image_config.run(url, product.articul, i)}
        product.images = saved_images
        excel_product = @get_excel_products.call.find{|p|p.articul == product.articul and not p.is_added}
        unless excel_product.nil?
          product.images = excel_product.images if product.images.length == 0
          product.sizes = excel_product.sizes if product.sizes.length == 0
          product.price = excel_product.price
          excel_product.is_added = true
        end
        add_product(product)
        add_product_new(product)
      else
        parse_offers(product_page, product, json['OFFERS'].group_by{|j|j['TREE']['PROP_266']}, get_json_articul(json['OFFERS']))
      end
    end

    def get_json_articul(offers)
      result = offers.find{|o|not o['DISPLAY_PROPERTIES'].match(/(?<articul>\d+(-\d+)?)/).nil? and
         o['DISPLAY_PROPERTIES'].downcase.to_s.include?('артикул') }
      return nil if result.nil?
      result['DISPLAY_PROPERTIES'].match(/(?<articul>\d+(-\d+)?)/).captures[0].to_s unless result.nil?
    end

    def parse_offers(product_page, product, json, json_articul)
      new_json = {}
      descr = product.description
      product.articul = json_articul unless json_articul.nil?
      excel_product = @get_excel_products.call.find{|p|p.articul == product.articul and not p.is_added}
      json.each do |key, offers|
        puts key
        color = product_page.search("li[data-treevalue='266_#{key}'] > .cnt > .cnt_item").first
        color = color.nil? ? '' : prepare_string(color.attr('title').to_s)
        color_articul = product.articul if color.blank?
        color_articul = "#{product.articul}~#{color}" unless color.blank?

        sizes = offers.map{|o|o['TREE']['PROP_268']}.map{|size|product_page.search("li[data-treevalue='268_#{size}'] > .cnt").first.text}
        pics = offers[0]['SLIDER'].map{|p|p['SRC']}
        saved_images = []
        pics.each_with_index { |url, i| saved_images <<  @image_config.run(url, color_articul, i)}

        product.description = "Цвет #{color}. #{descr}" unless color.blank?
        product.images = saved_images
        product.sizes = sizes
        excel_product = @get_excel_products.call.find{|p|p.articul == product.articul}
        unless excel_product.nil?
          product.images = excel_product.images if product.images.length == 0
          product.sizes = excel_product.sizes if product.sizes.length == 0
          product.price = excel_product.price
        end

        @validator.validate product
        add_product(product)
        new_json[color] = get_json(product)
      end
      product.description = descr
      add_product_new(product, new_json)
      excel_product.is_added = true unless excel_product.nil?
    end

    def with_excel_products(get_excel_products)
      @get_excel_products = get_excel_products
      self
    end
  end
end