#encoding:utf-8
require_relative '../framework/parser'
require_relative 'ledisharm_product_config'
require_relative '../alisa_fashion/alisa_fashion_image_config'
require 'htmlentities'

module Ledisharm
  class LedisharmParser < Framework::<PERSON><PERSON><PERSON>
    def initialize_settings
      with_url('http://shop.ledisharm.com/shop/')
          .with_category_links('.filtrlink')
          .with_pagination('[title*="Следующая"]')
          .with_product_config(->(pc) {
            pc.with_product_selector('.tovar-title > a')
                .with_category(->(product_page){
                  'Без категории'
                })
                .with_name(->(product_element) {
                  name = product_element.search('.tovtitle').first.text.split
                  prepare_string name.take(name.length - 1).join ' '
                })
                .with_articul(->(product_page) {
                  prepare_string product_page.search('.tov_id').first.text.split.last.gsub('.', ' ')
                })
                .with_price(->(product_page) {
                  text = HTMLEntities.new.decode(product_page.search('.newprice').first.text).gsub(' ', ' ').gsub(' ', '')
                  text.gsub(',', '.').gsub('.', ' ').to_i

                  # product_page.search('.newprice').first.text.gsub('.', ' ').gsub(' ', '').gsub('&nbsp;', '').to_i
                })
                .with_sizes(->(product_page) {
                  product_page.search('.tov_size_el')
                    .map{|opt| prepare_string opt.text.gsub('.', ',') }
                })
                .with_description(->(product_page) {
                  color = product_page.search('.tov_color_pic').first
                  return '' if color.nil?
                  color = color.attr('title')
                  color = color[1..-1] if color[0] == '-'
                  return '' if color.blank?
                  prepare_string "Цвет: #{color}"
                })
                .with_images(->(product_page) {
                  images = product_page.search('.sfoto > a').map{|a| a.attr('href').to_s}
                  if images.length == 0
                    images = product_page.search('.tov_img_zoom > a').map{|a| a.attr('href').to_s}
                  end
                  images
                })
                .with_image_config(->(ic) {
                  ic.with_default_url('http://shop.ledisharm.com')
                }, AlisaFashion::AlisaFashionImageConfig)
                .with_composition(->(product_page){
                  product_page.search('.tov_sostav_body').map{|div| prepare_string div.text}.join ', '
                })
                .with_category_type('61')
                .with_category_type_by_name('62', 'блуза')
                .with_category_type_by_name('65', 'брюки')
                .with_category_type_by_name('1919', 'вкладыши')
                .with_category_type_by_name('64', 'двойка')
                .with_category_type_by_name('66', 'джемпер')
                .with_category_type_by_name('65', 'джинсы')
                .with_category_type_by_name('82', 'жакет')
                .with_category_type_by_name('83', 'жилет')
                .with_category_type_by_name('1360', 'капри')
                .with_category_type_by_name('66', 'кардиган')
                .with_category_type_by_name('1916', 'колье')
                .with_category_type_by_name('64', 'костюм')
                .with_category_type_by_name('80', 'куртка')
                .with_category_type_by_name('347', 'лосины')
                .with_category_type_by_name('80', 'пальто')
                .with_category_type_by_name('96', 'платье')
                .with_category_type_by_name('80', 'плащ')
                .with_category_type_by_name('66', 'пончо')
                .with_category_type_by_name('115', 'ремень')
                .with_category_type_by_name('79', 'топ')
                .with_category_type_by_name('62', 'туника')
                .with_category_type_by_name('1360', 'шорты')
                .with_category_type_by_name('81', 'юбка')

          }, Ledisharm::LedisharmProductConfig)
    end
  end
end