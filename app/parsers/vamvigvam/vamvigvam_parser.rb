#encoding: utf-8

require 'downloader'
require 'spreadsheet'

module Vamvigvam
  class VamvigvamParser < Downloader

    def initialize
      @dlparams = {'price_file' => :file}
      super
    end




    def process_page(url,art,price,cat,name)
      @log.info(url)
      puts url
      page = @agent.get(url)

      needed_props=['Вес','Материал','Размер стандартный','Размер большой','Наполнение','Дерево','Ткань','Выбрать размер','Диаметр шара','Количество шаров']

      props=Hash.new
      page.search('.item-params .item-desc-row').each do |div|
        k=div.search('.item-desc-text').text.strip
        v=div.search('.item-desc-value').text.strip

        next unless needed_props.include? k
        next if k=='Размер большой' and not name.downcase.include? 'большой'
        next if k=='Размер стандартный' and name.downcase.include? 'большой'

        if k=='Выбрать размер'
          k='Размер'
          size_sel=page.search('select[name="Выбрать размер"]')
          if size_sel.count==1
            size_sel.search('option').each do |opt|
              t=opt.attr('value').to_s
              if t!=''
                if name.downcase.include? 'большой'
                  if t.downcase.include? 'большой'
                    v=t.gsub(/ \(\+.*/,'')
                    break
                  end
                else
                  if t.downcase.include? 'стандартный'
                    v=t.gsub(/ \(\+.*/,'')
                    break
                  end
                end
              end
            end
          end
        end
        props[k]=v if v and v!=''
      end

      desc=''

      if name.downcase.include? 'вигвам '
        desc=page.search('.block-type-catalogitem-text p').first.text.strip
      else
        desc=page.search('.block-type-catalogitem-text p').map {|p| p.text.strip}.reject {|p| p.downcase.include? 'желани'}.join ' '
      end

      desc+='.' unless desc.end_with? '.'
      desc+=' '

      prop_text=props.map {|k,v| "#{k.downcase}: #{v.downcase}"}.join', '

      desc+=prop_text.capitalize

      desc.gsub!(/^\. /,'')

      pic_urls=page.search('a.catalog-images-item-link').map {|a| a.attr('href').to_s}
      pics=[]
      pic_urls.each_with_index {|pic_url,i|
        pics<<savePic(pic_url,"#{art}~#{i}",true)

      }


      col=addCollection(cat,902)
      addProduct(col,art,name,price,desc,[],pics) #unless @products.include? art

    rescue Mechanize::Error => e
      return if e.response_code == '404'
      raise e
    end

    def process_file(file)
      book = RubyXL::Parser.parse file
      sheet=book[0]

      cat=''
      sheet.sheet_data.rows.each_with_index do |row, index|
        next if row[7] and row[7].value

        next if row[0].nil?

        name=row[0].value
        cat=name if row[4].value.nil? and name

        next if row[4].value.nil? or row[4].value=='Опт'

        hl = sheet.hyperlinks.find{ |hl| hl.ref == row[0].r }
        if hl
          ext_link = sheet.relationship_container.find_by_rid(hl.r_id)
          next if ext_link.nil?
          page_href=ext_link.target
        end

        next if page_href.nil?

        next if row[1].nil?

        art=row[1].value
        price=row[4].value.to_f.ceil

        process_page(page_href,art,price,cat,name)
      end

    end

    def run
      if @agent==nil then before end

      process_file(@price_file)
    end

  end
end

