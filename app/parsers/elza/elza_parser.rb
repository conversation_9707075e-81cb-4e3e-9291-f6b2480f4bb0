module Elza
  class ElzaParser < Downloader
    @dlparams={'login'=>:text,'password'=>:password}

    def initialize
      @dlparams={'login'=>:text,'password'=>:password}
      super
    end

    def process_page(href)
      puts href

      begin
        @page = @agent.get(href)
      rescue
        return
      end

      product_id = @page.search('meta[name="product-id"]')[0].attr('content')
      product_data = @agent.get("https://elza-nsk.ru/products_by_id/#{product_id}.json")
      json_data = JSON.parse(product_data.body)['products'][0]

      return unless json_data['available']

      category = @page.search('.breadcrumbs_Breadcrumbs a').last.text

      art2 = json_data['title']

      art = URI::Parser.new.unescape(json_data['url'].split('/')[-2..-1].join('_'))
      name = json_data['title']
      color = (name.match(/(\()(.+)(\))/) && name.match(/(\()(.+)(\))/)[2]) || "Без названия"

      clothes_params = json_data['characteristics'].map{|c| c['title']}.join('\n')
      desc = ActionController::Base.helpers.strip_tags(HTMLEntities.new.decode(json_data['short_description']))
      desc = '' if desc.nil?
      desc += "\n#{clothes_params}"
      desc.gsub!(/^, /,'')

      prices = {}
      json_data['variants'].each do |variant|
        next unless variant['available']
        price = variant['price'].to_i
        prices[price] = [] unless prices[price]
        prices[price] << variant['title']
      end

      pics = []
      json_data['images'].each_with_index do |image, i|
        file_name = "#{art}~#{i}"
        savePic(image['original_url'], file_name, false)
        pics << file_name
      end

      colors = {}
      max_price = (prices.keys.max * 1.15).to_i

      colors[color] = {
        sizes: prices.map do |price, size|
          size.map do |s|
            {
              s => {
                price: (price.to_i*1.1).ceil,
                stock_price: price.to_i,
                #retail_price: price.to_i + (price.to_i * 1.15).to_i,
                weight: 0.5
              }
            }
          end
        end.flatten.reduce(&:merge),
        pics: pics.map do |pic|
          f = File.open("#{@picpath}#{pic}.jpg")
          hash = {
            path: pic,
            size: f.size,
            hash: Digest::MD5.hexdigest("#{pic}#{f.size}")
          }
          f.close
          hash
        end
      }


      cat_type='1101'
      cat_type2 = ['Женщинам', 'Одежда', category]

      col=addCollection(category,cat_type)
      prices.each {|price,sizes|
        addProduct(col,art+' '+art2,name,price,desc,sizes,pics)
        addProductNew(href, category, cat_type2, art, name, desc, colors, max_price)
      }
    end

    def process_cat(href)
      puts "Cat #{href}"
      page = @agent.get(href)
      pages = []

      page.search('#product_list .item a').each {|a|
        pages << (a.attr('href').to_s)
      }

      pages.each{|pg| process_page(pg)}
    end

    def run
      page = @agent.get 'https://elza-nsk.ru/'
      categories = []

      page.search('#header .moduletable-category ul li a').each {|a|
        next if a.text == 'СКОРО В ПРОДАЖЕ' || a.text == 'Скоро в продаже'
        href = a.attr('href')
        categories << (href + "?page_size=1000")
      }
      categories.each{|cat| process_cat(cat)}
    end
  end
end

