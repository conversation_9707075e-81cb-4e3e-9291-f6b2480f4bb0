module FunnyBaby
  class FunnyBabyOrderSender < Framework::OrderSender
    def initialize
      super
      @file_result=true
      @dlparams = {'price_file' => :file}.merge!(@dlparams)
      @result={}
    end

    def add_csv_data(fields)
      articul = fields['Артикул'].to_s.split.last
      size = fields['Размер'].to_s
      color=''
      color = fields['Наименование'].to_s.split(' цвет ').last if fields['Наименование'].include? ' цвет '

      @csv_data[articul] = {} unless @csv_data.include? articul
      @csv_data[articul][color] = {} unless @csv_data[articul].include? color

      @csv_data[articul][color][size]=0 unless @csv_data[articul][color][size]
      @csv_data[articul][color][size]+=1
    end

    def order(articul, colors)
      colors.each do |color, sizes|
        sizes.each do |size,q|
          barcode=Cache.get_value('funny_baby',"#{articul}~#{size}~#{color}")
          @log.info "not found: #{articul}~#{size}~#{color}" if barcode.nil?
          @result[barcode.to_s]=q
        end
      end

    end

    def process
      @purchase.status=10
      @purchase.save
      before unless   Rails.env.development?

      super

      #@price_file=Cache.get_value('mann','price_file')
      book = RubyXL::Parser.parse @price_file
      book[0].sheet_data.rows[1..-1].each do |row|
        next if row[4].nil?
        code=row[4].value.to_s
        next unless /^\d{13}$/=~code
        row[10].raw_value=@result[code]
        @result.delete(code)
      end

      @log.info "Left: #{@result}"

      d=DateTime.now
      df=d.strftime('%Y%m%d')

      after(nil)

      return ["funnybaby-#{df}.xlsx",'application/vnd.ms-excel',book.stream.string]

    end
  end

end

