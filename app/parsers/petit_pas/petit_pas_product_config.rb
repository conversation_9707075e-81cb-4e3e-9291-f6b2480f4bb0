#encoding:utf-8
require_relative '../framework/site/product_config'
module PetitPas
  class PetitPasProductConfig < Framework::Site::ProductConfig
    def get_color(product_page)
      text = prepare_string product_page.search('.itemOptions').first.text
      if text.include? '_'
        return text.split('_').drop(1).join('_')
      end

      text.match(/Артикул:\s*(?<articul>[A-Z\d]+)_?(?<color>.*)/)['color']
    end

    def parse_product(product_page, link=nil)
      @color = get_color(product_page)
      result = super(product_page, link)
      result.category = "#{result.category} - Бестселлеры" unless product_page.search('#bigImageItem .bestFlag').first.nil?
      if result.images.length == 0
        @error_log_func.call("#{link} have not images")
        return nil
      end
      result
    end

    def save_images(product_elem, articul)
      super(product_elem, "#{articul}~#{@color}")
    end

    def add_product(product)
      p = product.clone
      p.name = "#{product.name} #{@color}"
      return nil unless @parser.prices[p.articul]

      p.price=@parser.prices[p.articul]
      super(p)
    end

    def add_product_new(product, json=nil, cat_type=nil)
      existing_product = ProductNew.find_by(purchase_id: @purchase.id, sku: product.articul)
      if existing_product.nil?
        super(product, json, cat_type)
        return
      end

      colors=JSON.parse(existing_product.colors)
      get_json(product, @color).each { |key, value| colors[key] = value }

      existing_product.colors = colors.to_json
      existing_product.save!
    end

    def create_new_product(product_page, link=nil, sub_req=false)
      super(product_page, link)

      return if sub_req

      sub_product_urls = product_page.search('#sameProductToItem > a').map { |a| a.attr('href') }
      sub_product_urls.each { |u|
        p = get_page(u)
        create_new_product p, u, true
      }
    end
  end
end