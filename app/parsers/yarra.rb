#encoding: utf-8

require 'downloader'
#require 'logger'
#require 'unicode_utils/downcase'

class Yarra < Downloader
  attr_accessor :login
  attr_accessor :password

  def initialize
    @dlparams={'login'=>:text,'password'=>:password}
    super
  end


  def processPage(url,cat,get_sizes=false)

    @log.info('page '+url)

    puts url

    page=@agent.get url

    page_id=page.search('/udata/page/@id').to_s

    desc_fields=['metall','osnovnaya_vstavka','weight']

    art=page.search('//property[@name="artikul"]/value/text()').to_s
    type=page.search('//property[@name="vid_izdeliya"]/value/item/@name').to_s
    price=page.search('//property[@name="price0"]/value/text()')[0].to_s.to_f*0.7
    price=price.ceil.to_s

    type.gsub!('талис.','талисманов')
    type.gsub!('талисм.','талисманов')
    type.gsub!('с 2 по.','')
    type.strip!

    desc=''

    puts art
    puts price

    name=page.search('//name/text()').to_s

    page.search('//property').each do |prop|
      if desc_fields.include? prop.attr('name')
        val=prop.search('./value/item/@name').to_s
        val=prop.search('./value/text()').to_s if val==''
        desc+=' '+prop.search('./title/text()').to_s+': '+val+'.'
      end
    end

    desc.gsub!(/&[a-z]+;/,'')
    desc=ActionView::Base.full_sanitizer.sanitize(desc)


    pics=[]
    page.search('//property[@type="img_file"]/value/text()').each_with_index do |href,i|
      savePic(href,"#{page_id}~#{i}",false)
      pics<<"#{page_id}~#{i}"
    end
    puts pics

    sizes=[page.search('//property[@name="razmer"]/value/text()').to_s.gsub(',','.')]

    if get_sizes
      page=@agent.get (url+'?transform=yarra/modules/catalog/popup-good-info.xsl')
      page=Nokogiri::HTML(page.body)
      sizes=page.search('label').map{|l| l.text.gsub(',','.')}
    end

    col=addCollection(cat+'-'+type,"88")
    addProduct(col,art,name,price,desc,sizes,pics)


  rescue Mechanize::Error => e
    puts e
    @log.info e
    print e.backtrace.join("\n")
    retry unless (tries-=1).zero?

  end

  def processCat(href,catName)
    puts href
    @log.info('cat '+href)
    page = @agent.get(href)

    return true if page.body.include? 'no data'

    #page.search("a.basket_list").each do |a|
    #  pid=a.attr('id').gsub('add_basket_','')
    #  processPage('http://yarra.ru/upage/'+pid,catName)
    #end

    page.search("a.ti_but").each do |a|
      pid=a.attr('href').gsub('/emarket/basket/put/element/','').gsub('/','')
      processPage('http://yarra.ru/upage/'+pid,catName,true)
    end

    return false

  rescue Mechanize::Error => e
    @log.info e
    puts e
    print e.backtrace.join("\n")
    retry unless (tries-=1).zero?
    return true

  end

  def run
    #page = @agent.get('http://yarra.ru/')

    #login_form=page.forms[0]
    #login_form.login=@login
    #login_form.password=@password

    #page = @agent.submit(login_form, login_form.buttons.first)

    page=@agent.get('http://yarra.ru/catalogue/')

    page.search("aside ul li a").each do |a|

      if /brend=(.*)/=~a.attr('href')
        brend=$1
        cat_name=a.text.strip
        done=false
        p=0
        while not done
          done=processCat("http://yarra.ru/catalog/getObjectsListBrend/?mode=json&brend=#{brend}&p=#{p}",cat_name)
          p+=1
        end
      end
    end

  end
  #handle_asynchronously :run

end