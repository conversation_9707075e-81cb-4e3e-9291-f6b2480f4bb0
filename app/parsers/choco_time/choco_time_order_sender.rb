#encoding:utf-8

require_relative '../framework/order_sender'

module ChocoTime
  class ChocoTimeOrderSender < Framework::OrderSender

    def initialize
      # @dlparams = {'agent_header' => :text}
      @file_result = true
      super
    end


    def process
      @csv_data=Hash.new(0)
      parse_data
      txt="//delete window.localStorage.tcart\n\n"+
        "function timeout(ms) {    return new Promise(resolve => setTimeout(resolve, ms));}\n"+
        "for (;;) { if ($('.js-store-load-more-btn td:visible').length==0) break; $('.js-store-load-more-btn td:visible').each((i,div)=>{div.click()}); await timeout(5000);}\n"+
        "const products=[];\n";

      @csv_data.each do |href, q|
        if /tproduct\/\d+-(\d+)/=~href
          lid=$1
          txt+="products.push(['#{lid}',#{q}])\n"
        end
      end

      txt+="for(i=0;i<products.length;i++) {\n"+
        "console.log(products[i][0])\n"+
        "if ($(`div[data-product-lid=${products[i][0]}] a[href=\"#order\"]`)[0]) {\n"+
        "$(`div[data-product-lid=${products[i][0]}] a[href=\"#order\"]`)[0].click(); await timeout(2500);\n"+
        "} else { console.log('Not found') }}\n"

      txt+="console.log('Закончено')\n\n"

      txt+="d=JSON.parse(window.localStorage.tcart)\n"

      txt+="for(i=0;i<products.length;i++) {\n"+
        "t=d.products.find(p=>p.lid==products[i][0].toString())\n"+
        "if (t) { t.quantity=products[i][1]; t.amount=t.price*products[i][1]}\n"+
        "}\n"

      txt+="window.localStorage.tcart=JSON.stringify(d)\n"

      d=DateTime.now
      df=d.strftime('%Y%m%d')

      file_name = "vekshokolada_order-#{df}.txt"

      #File.write(Dir.tmpdir+'/'+file_name,order_text)
      [file_name, 'text/plain', txt]
    end


    def add_csv_data(fields)
      articul = fields['Источник товара'].to_s
      @csv_data[articul]+=1
    end
  end
end