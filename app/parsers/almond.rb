#encoding: utf-8

require 'downloader'
#require 'logger'
#require 'unicode_utils/downcase'

class Almond < Downloader
  attr_accessor :login
  attr_accessor :password

  def initialize
    @dlparams={'login'=>:text,'password'=>:password}
    super
  end


  def processPage(url,cat)
    tries ||= 2

    @log.info('page '+url)

    puts url

    page = @agent.get(url)
    conv=Encoding::Converter.new('Windows-1251','UTF-8')
    body=conv.convert(page.body)
    page = Nokogiri::HTML(body)

    if %r{item/([0-9]+)/}=~url
      art=$1
    else
      return
    end

    puts art

    discount=false
    if page.search("//div[@class='price_tov']/span/strike").length>0 then discount=true end
    price=page.search("//div[@class='price-card']//span/text()")[0].to_s.sub('руб.','').strip

    if discount then cat=cat+" - скидки" end

    name=page.search('//h1[@itemprop="name"]/text()').to_s.strip

    desc=Array.new
    page.search("//nav[@class='tov_attr']/ul/li").each do |li|
      desc.push(li.search('./span[@class="title"]/text()')[0].to_s+" "+li.search('./span[@class="value"]/text()')[0].to_s)
    end
    desc=desc.join('. ')
    desc.gsub!(/ +/,' ')
    puts desc

    sizes=nil
    colorCode=nil
    color=nil
    pic=nil

    page.search('//form[@id="sizesform"]/div').each do |div|
      if div['class']=='gt-title'
        color=div.search('p.model_color').text.gsub('Цвет:','').strip
        sizes=Array.new
        pic=nil
        div.search('a.model_size_link').each do |img|
          pic=nil
          if /largeimage: '(.*?)'/=~img.attr('rel')
            pic=$1
            break
          end
        end
        next
      end
      if div['class']=='gt-sizes'
        div.search('.//input').each do |input|
          sizes.push(input['id'])
          colorCode=input['name']
        end
        puts color
        puts colorCode
        puts sizes
        puts pic

        if pic!=nil
          savePic(pic,"#{art}-#{colorCode}",false)
        end
        cat.gsub!(/\([0-9]+\)/,'')
        col=addCollection(cat,'850')
        addProduct(col,"#{art}-#{colorCode}",name+', '+color,price,'Цвет '+color+'. '+desc,sizes,["#{art}-#{colorCode}"])

      end
    end



    return


  rescue Mechanize::Error => e
    puts e
    @log.info e
    print e.backtrace.join("\n")
    retry unless (tries-=1).zero?

  end

  def processCat(href,catName)
    tries ||= 2
    puts href
    @log.info('cat '+href)
    return if href.include? 'status/33'
    page = @agent.get(href)

    puts 'a'

    hasSubcat=false
    page.search("//li[@class='current']/ul/li/a").each do |a|
      processCat(a['href'],catName+' - '+a.text)
      hasSubcat=true
    end

    if hasSubcat then return end

    page.search("//div[@class='catalog_item ']/div[@class='ci-description']/a/@href").each do |href|
      processPage(href,catName)
    end

    rescue Mechanize::Error => e
      @log.info e
      puts e
      print e.backtrace.join("\n")
      retry unless (tries-=1).zero?

  end

  def run
    page = @agent.get('http://www.almondshop.ru/')

    login_form=page.forms[1]
    login_form.login_name=@login
    login_form.login_password=@password

    page = @agent.submit(login_form, login_form.buttons.first)

    @agent.get('http://www.almondshop.ru/engine/ajax/filter.php?action=setlimit&limit=1000&category=4')

    page = @agent.get('http://www.almondshop.ru/catalog/')


    page.search("//nav[@class='catalog_menu']/ul/li/a").each do |a|
        processCat(a['href'],a.text)
    end

  end
  #handle_asynchronously :run

end