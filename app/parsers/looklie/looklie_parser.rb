module Looklie
  class LooklieParser < Downloader
    def initialize
      @url = "https://www.looklie.ru/"
      @dlparams = {}
      @collection = nil
      @login = "<EMAIL>"
      @password = "270279"

      super
    end

    def process_clothes(href, category)
      @log.info "Process clothes #{href}"
      page = @agent.get(href)

      # описание товара
      characteristics_table = page.search('div.popup_looklie_full_description_container > table')
      page.search('div.popup_looklie_full_description_container > table').remove
      description = page.search('div.popup_looklie_full_description_container').text

      characteristics = {}
      characteristics_table.search('tr').each_with_index do |tr, index|
        characteristic_key = tr.search('td')[0].text.gsub(/\s+/, ' ').strip
        characteristic_value = tr.search('td')[1].text.gsub(/\s+/, ' ').strip
        characteristics[characteristic_key] = characteristic_value
      end

      # артикул
      vendor_code = characteristics.delete('Артикул')

      # описание с характеристиками вместе
      details = characteristics.map { |key, value| "#{key}: #{value}" }.join ', '
      description += ". " + details

      # # наименование товара
      name = page.search('div.popup_looklie_head > h1 > span').text.gsub(/\s+/,' ').strip + " " + vendor_code

      name.gsub!(vendor_code,'')
      name.strip!

      # изображения товара
      pictures = []
      main_picture_url = page.search('img.looklie_album_view_image').attr('src').to_s
      pictures_carousel = page.search('div.looklie_album_preview ul > li')

      # проверяем, если картинка единственная
      if !pictures_carousel.to_s.empty?
        last_index = 0
        pic_urls = pictures_carousel.each_with_index {|li, index|
          picture_url = li.search('a').attr('href').to_s
          pictures << savePic(picture_url, "#{vendor_code}~#{index}", true)
          last_index = index
        }
        pictures << savePic(main_picture_url, "#{vendor_code}~#{last_index + 1}", true)
      else
        pictures << savePic(main_picture_url, "#{vendor_code}~0", true)
      end

      col_type=guess_cat_type(category,name,'850')

      @collection = addCollection(category, col_type)
      sizes = Hash.new { |h, k| h[k] = [] }

      size_ids={}

      is_sale=false

      # цена и размер
      page.search('table.looklie_form_column_one > tbody > tr').each_with_index {|tr, index|
        if tr.search('td')[2].at('div.looklie_form_price > span.looklie_form_price_number strike')
          price = tr.search('td')[2].at('div.looklie_form_price > span.looklie_form_price_number strike').text.gsub(/[^\d.]/, '')
          #@collection = addCollection("#{category} - скидки" , col_type)
          #@collection.disabled=false
          #@collection.remove_tag('Выкл')
          #@collection.save
          is_sale=true
        else
          price = tr.search('td')[2].at('div.looklie_form_price > span.looklie_form_price_number').text.gsub(/[^\d.]/, '')
        end
        size=tr.search('td')[0].at('label.looklie_form_size_handler').text.strip.gsub(/i$/,'').strip
        sizes[price]<<size
        size_id=tr.search('input[data-looklie-id]').first.attr('data-looklie-id').to_s
        size_ids[size]=size_id

        sizes['rrp']=Hash.new if sizes['rrp'].nil? or sizes['rrp'].is_a? Array
        sizes['rrp'][price]=(price.to_f*1.6).ceil
      }

      Cache.set_value('looklie',vendor_code,sizes)
      Cache.set_value('looklie',"#{vendor_code}_url",href)

      prod=addProduct(@collection, vendor_code, name, 999999999, description, sizes, pictures, sale: is_sale)
      prod.collections=[@collection]
      prod.save

        # sleep(rand(0.3)+0.3)

    rescue Mechanize::Error => e
      puts e
      print e.backtrace.join("\n")
      retry unless (tries-=1).zero?

    end

    def process_clothes_list(href, category, subcategory=nil)
      @log.info "Process clothes list #{href}"

      page = @agent.get(href)
      category += " - " + subcategory if subcategory

      loop do

        page.search('div.looklie_list.clearfix > ul > li.looklie_list_item').each_with_index {|li, index|
          a = li.search('div.looklie_list_image > a').attr('href')
          process_clothes(a.to_s, category)
        }

        last_pagination_link = page.search('div.looklie_header.clearfix > div.looklie_pages.clearfix > a').last
        break if last_pagination_link.nil?
        is_next_page_link = last_pagination_link.search("label").first
        break if is_next_page_link.nil?

        next_page = last_pagination_link.attr('href').to_s
        page = @agent.get(next_page)
      end
    end

    # процедура входа
    def sign_in
      url = "https://looklie.ru/login/process"
      page = @agent.get(url)
      login_form = page.form_with(name:'f_form_user')
      login_form['f_email'] = @login
      login_form['f_password'] = @password
      page = @agent.submit(login_form, login_form.buttons[0])
    end

    def run
      # входим
      sign_in

      process_clothes_list('https://looklie.ru/shop?lf_sx[4]=1&lf_pr[1]=2100&f_btn_apply=&split=9000',"Для женщин")
      process_clothes_list('https://looklie.ru/shop?lf_sx[8]=1&lf_pr[1]=2100&f_btn_apply=&split=9000',"Для мужчин")

      page = @agent.get(@url)
      page.search("div.box9.clearfix > div.h_catalog_column.clearfix").each_with_index {|div, index|
        next if index == 0
        category = div.search('div.h_catalog_title').text
        div.search('ul').each {|ul|
          ul.search('li').each {|li|
            subcategory = li.search('a').text
            process_clothes_list(li.search('a').attr('href').to_s, category, subcategory)
          }
        }
      }
    end
  end
end
