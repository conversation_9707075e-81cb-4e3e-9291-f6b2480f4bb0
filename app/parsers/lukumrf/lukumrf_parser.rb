module Lukumrf
  class LukumrfParser < Downloader
    include InvoiceSimpleLast

    @dlparams={'login'=>:text,'password'=>:password}

    def initialize
      @dlparams={'login'=>:text,'password'=>:password}
      super
    end

    def process_page(href)
      puts href

      page=@agent.get href

      return if page.search('link[itemprop=availability]').count==0

      cat=page.search('.breads span[itemprop=name]')[2..3].map(&:text).join('-')
      puts cat

      if /(\d+)$/=~href
        art=$1
      end
      puts art

      name=page.search('h1[itemprop=name]').first.text.to_s
      puts name

      price=page.search('meta[itemprop=price]').attr('content').to_s
      price=page.search('.details-payment-price .price .price-old .price-number').text.gsub(' ','').gsub(',','.') if page.search('.details-payment-price .price .price-old').count>0

      price=price.to_f.ceil
      puts price

      return if price==0

      desc=page.search('.tabs-content .details-tabs-deacription').text.to_s.gsub(/[\r\n ]+/,' ').gsub('&nbsp;',' ').gsub(/ +/,' ').strip

      desc=desc.gsub(name,'').strip

      pic=page.search('.gallery-block a').first.attr('href').to_s

      cat_type='100343'

      cat_type='100345' if cat.include? 'сточные'
      cat_type='100349' if cat.include? 'онфеты'
      cat_type='100348' if cat.include? 'ефир'
      cat_type='100347' if cat.include? 'афли' or cat.include? 'еченье'

      pic=savePic(pic,art,false)

      col=addCollection(cat,cat_type)
      addProduct(col,art,name,price,desc,[],[pic])

    end

    def process_cat(href)
      puts href

      page=@agent.get href

      page.search('.products-view  a.products-view-name-link').each {|a|
        process_page(a.attr('href').to_s)
      }

    end

    def run
      if @agent==nil then before end

      page=@agent.get 'http://opt.lukumrf.ru/login'
      login_form=page.form_with(name:'authForm')
      login_form['email']=@login
      login_form['password']=@password
      page = @agent.submit(login_form, login_form.buttons[1])

      page.search('ul.menu-general ul.menu-general-submenu li a').each {|a|
        process_cat(a.attr('href').to_s)
      }

    end
  end
end
