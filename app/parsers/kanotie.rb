#encoding: utf-8

require 'downloader'
#require 'logger'
#require 'unicode_utils/downcase'

class Kanotie < Downloader
  include InvoiceKanotie

  def initialize
    @dlparams={}

    @bad_cats=['KAMEA','КАМЕА','KORK<PERSON><PERSON>','NIKOL','VERSI<PERSON>','ПриКиндер','<PERSON><PERSON>','Infante',
    'Concept','<PERSON><PERSON><PERSON>']

    @bad_cats=@bad_cats.map{|c| c.upcase}
    super
  end


  def processPage(url)
    @log.info(url)
    page = @agent.get(url)

    cat=page.search('.breadcrumb a')[1..2].map(&:text).join('-')

    name=page.search('#content h1')[0].text.strip

    return if name=='Товар не найден!'

    return if page.search('.description a')[0].nil?

    brand=page.search('.description a')[0].text.downcase
    return if @bad_cats.any? {|b| brand.include? b.downcase}

    art=url.split('/')[-1]

    if /product_id=([0-9]+)/=~art
      art="~pid#{$1}"
    end

    art2=''
    page.body.force_encoding 'utf-8'

    if %r!span>Модель:</span>(.*)<!=~page.body
      art2=$1
      art2.strip!
      name.gsub!(' '+art2,'')
      name.strip!
    end

    sale=true
    price=page.search('.product-info span.price-new')
    if price.length==0
      sale=false
      price=page.search('.product-info div.price')
    end

    return if price.length==0

    price=price[0].text.gsub(' р.','')

    stock=page.search('span.stock')[0].text.strip
    if stock!='Есть в наличии' then return end

    minAmount=1
    t=page.search('.minimum')
    if t.length>0
        if /Минимальное количество заказа этого товара: ([0-9]+)/=~t[0].text
          minAmount=$1.to_i
        end
    end

    desc=page.search('#tab-description')[0].text.strip.gsub("\r\n\r\n","\r\n").gsub('  ',' ').gsub("\t","").gsub("\r\n",", ")

    return if page.search('.product-info .image a').length==0
    pic=page.search('.product-info .image a')[0].attr('href')

    colors=[]
    add=0
    page.search('div.options option').each do |o|
      next if o.text.include? '--- Выберите ---'
      next if o.text.include? 'ассорти'
      if /\(\+(.*?) ..\)/=~o.text
        add=$1.to_f if $1.to_f>add
      end
      t=o.text.strip.gsub(/ +/,' ')
      t.gsub!(/\(.*? ..\)/,'')
      colors<<t.strip.gsub(',','-')
    end

    page.search('div.options input[type=radio]').each do |o|
      txt=page.search("label[for=#{o.attr('id')}] img")[0].attr('alt')
      next if txt.include? '--- Выберите ---'
      if /\(\+(.*?) ..\)/=~txt
        add=$1.to_f if $1.to_f>add
      end
      t=txt.strip.gsub(/ +/,' ')
      t.gsub!(/\(.*? ..\)/,'')
      colors<<t.strip.gsub(',','-')
    end

    price=price.to_f+add

    price*=1.2 if desc.include? '20%' and add==0

    desc.gsub!('Теперь можно заказать по одной паре, выбрав цвет, но на 20% дороже.','')

    #price*=0.97

    image=savePic(pic,art,true)

    cattype='850'
    cattype='2426' if page.search('.breadcrumb').text.include? 'ДЕТСКИЕ'
    cattype='870' if page.search('.breadcrumb').text.include? 'МУЖСКИЕ'

    cat+=' - РАСПРОДАЖА' if sale


    if minAmount>1
      desc="Упаковка #{minAmount} шт! "+desc
    end

    col=addCollection(cat,cattype)
    addProduct(col,(art+' '+art2).strip,name,price,desc,colors,[image],nil,0,nil,sale:sale)

  end

  def processCat(url,top=true)
    @log.info(url)

    if url.include? 'soloma' then return end

    #return if not (url.include? 'shapki' or url.include? 'mitten')

    page = @agent.get(url)

    page.search(".product-list div.name a").each do |a|

      if / ([^ ]*)$/=~a.text.strip
        processPage(a.attr('href'))
      end
    end

    if top
      visited=[]
      page.search(".pagination a").each do |a|
        processCat(a.attr('href'),false)
      end

    end
  end

  def run
    if @agent==nil then before end
    page = @agent.get('http://www.kanotie.com/index.php?route=account/login')

    login_form=page.forms[1]
    login_form.email='<EMAIL>'
    login_form.password='84c3762d92'
    page = @agent.submit(login_form, login_form.buttons.first)

    page=@agent.get 'http://www.kanotie.com/'

    page.search('#menu ul ul a').each do |a|
      #next if @bad_cats.include? a.text.upcase
      next if ['ANTAL','FIGL','RYLKO'].include? a.text

      processCat(a.attr('href'))
    end

  end
  #handle_asynchronously :run

end