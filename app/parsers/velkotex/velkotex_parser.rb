#encoding:utf-8
require_relative '../framework/parser'
require_relative 'velkotex_product_config'

module Velkotex
  class VelkotexParser < Framework::<PERSON><PERSON><PERSON>
    def get_page_params(product_page)
      titles = product_page.search('.lsnn dt').map{|dt| prepare_string dt.text}
      values = product_page.search('.lsnn dd').map{|dd| prepare_string dd.text}

      Hash[titles.zip(values)]
    end

    def initialize_settings
      # У каждого размера могут быть свои цены
      # Разные цвета
      # Могут быть цвета без размеров
      with_url('http://velkotex-shop.ru/')
          .with_category_links('.root-item > a')
          .with_product_config(->(pc) {
            pc.with_product_selector(->(category_page) {
              category_page.search('.R2D2').to_a.find_all { |div| div.search('.notavailable').length == 0 }.map { |div| div.search('.item_img_bl > a').to_a }
            })
                .with_category(->(product_page){
                  product_page.search('.breadcrumb-item span').drop(1).map{|span|prepare_string span.text}.join ', '
                })
                .with_articul(->(product_page) {
                  get_page_params(product_page)['Артикул:']
                })
                .with_name(->(product_page) {
                  articul = get_page_params(product_page)['Артикул:']
                  prepare_string product_page.search('h1').first.text.gsub(articul, '')
                })
                .with_description(->(product_page) {
                  prepare_string product_page.search('[itemprop=description]').first.text
                })
                .with_image_config(->(ic){
                  ic.with_default_url('http://velkotex-shop.ru/')
                })
                .with_composition(->(product_page) {
                  get_page_params(product_page)['Состав:']
                })
                .with_producer(->(product_page){
                  get_page_params(product_page)['Производитель:']
                })
                .with_sizes(->(product_page) {
                  []
                })
                .with_category_type('850')
                .with_category_type('547', 'девочек', 'комплекты')
                .with_category_type('549', 'девочек', 'комбинезоны')
                .with_category_type('960', 'девочек', 'куртки')
                .with_category_type('528', 'девочек', 'брюки')
                .with_category_type('962', 'девочек', 'костюмы')
                .with_category_type('52', 'женщин', 'жилеты')
                .with_category_type('942', 'женщин', 'брюки')
                .with_category_type('1395', 'женщин', 'комплекты')
                .with_category_type('26', 'женщин', 'комбинезоны')
                .with_category_type('945', 'женщин', 'куртки')
                .with_category_type('949', 'женщин', 'костюмы')
                .with_category_type('561', 'мальчиков', 'брюки', 'зимние')
                .with_category_type('560', 'мальчиков', 'комплекты')
                .with_category_type('960', 'мальчиков', 'куртки')
                .with_category_type('561', 'мальчиков', 'брюки', 'спортивные')
                .with_category_type('962', 'мальчиков', 'костюмы', 'спортивные')
                .with_category_type('2497', 'мужчин', 'комплекты')
                .with_category_type('2497', 'мужчин', 'комбинезоны')
                .with_category_type('957', 'мужчин', 'куртки')
                .with_category_type('951', 'мужчин', 'брюки')
                .with_category_type('956', 'мужчин', 'костюмы')
                .with_category_type('954', 'мужчин', 'толстовки')
          }, Velkotex::VelkotexProductConfig)
    end
  end
end