#encoding:utf-8
require 'digest/md5'
require 'csv'
require 'my_print_bar/my_print_bar_product_config'
class Runner
  def self.add_purchase(name, dlclass)
    p=Purchase.new
    p.user_id=1
    p.name=name
    p.dlclass=dlclass
    p.save
  end

  def self.delete_purchase(id)
    purchase= Purchase.find(id)
    purchase.destroy
  end

  def self.update_product_json
    image_path = Spup2::Application.config.image_path
    ProductNew.includes(:purchase).find_each do |product|
      next if product.purchase.nil?
      next if product.colors.nil?
      puts product.purchase_id
      puts product.colors
      colors=JSON.parse(product.colors)
      picpath = image_path + product.purchase.dlclass

      if colors.is_a? Array
        sizes = {}
        colors.each { |c| sizes[c]=product.price }
        old_product = Product.joins(:collection).where(collections: {purchase_id: product.purchase_id}, art: product.sku).first
        pics = old_product.pics.split('~~~')
        colors = {'' => {'sizes' => sizes, 'pics' => pics}}
      end
      # Если значений нет или они уже обработаны
      next if colors.values.first.nil?
      next if colors.values.first['sizes'].nil?
      next if colors.values.first['sizes'].values.first.nil?
      next if colors.values.first['sizes'].values.first.is_a? Hash

      colors.each do |c, json|
        json['sizes'].each { |size, price|
          json['sizes'][size] = {'price' => price, 'weight' => 0.2}
        }
        json['sizes']['default'] = {'price' => product.price, 'weight' => 0.2} if json['sizes'].length == 0
        json['pics'] = json['pics'].map { |pic|
          # Разные оси. Для работы на тестовой машине
          next if pic.is_a? Hash
          if pic.starts_with? '/' and not picpath.starts_with? '/'
            {'path' => pic, 'hash' => '', 'size' => 0}
          else
            path = pic
            path = "#{picpath}/#{pic}" if pic[0] != picpath[0]
            path = path + '.jpg' unless path.include? '.jpg'
            if File.exist? path
              hash = Digest::MD5.hexdigest(File.read(path))
              size = File.size(path)
              {'path' => pic, 'hash' => hash, 'size' => size}
            else
              {'path' => pic, 'hash' => '', 'size' => 0}
            end

          end
        }
      end

      product.colors = colors.to_json
      product.save!
    end
  end

  def self.update_product_price
    ProductNew.find_each do |product|
      next if product.purchase.nil?
      next if product.colors.nil?
      puts product.purchase_id
      puts product.colors
      colors=JSON.parse(product.colors)
      next if colors.is_a? Array
      next if colors.values.first.nil?
      next if colors.values.first['sizes'].nil?
      next if colors.values.first['sizes'].values.first.nil?
      colors.each do |c, json|
        json['sizes'].each { |size, size_json|
          unless size_json.is_a? Hash
            size_json ={'price' => size_json, 'weight' => 0.2}
            json['sizes'][size] = size_json
          end

          if size_json['stock_price'].nil?
            price = size_json['price'].to_f
          else
            price = size_json['stock_price'].to_f
          end

          json['sizes'][size]['price'] = get_price(price, 1.3)
          json['sizes'][size]['stock_price'] = price
          if json['sizes'][size]['retail_price'].nil?
            json['sizes'][size]['retail_price'] = get_price(price, 1.7, true)
          end
        }
      end

      product.colors = colors.to_json
      product.save!
    end
  end

  def self.update_new_product_categories
    ProductNew.includes(:purchase).where.not(purchase_id:84).find_each do |product|
      next if product.purchase.nil?
      puts product.purchase_id
      puts product.sku
      old_product = Product.joins(:collection).where(collections: {purchase_id: product.purchase_id}, art: product.sku).first
      if old_product.nil?
        if product.purchase_id == 83
          is_man = product.cat.downcase.to_s.include?('мужские')
          product.cat_type = ['Женщинам', 'Одежда'].to_json unless is_man
          product.cat_type = ['Мужчинам', 'Одежда'].to_json if is_man
          puts product.cat_type
          product.save!
        end
        next
      end
      puts 'old'
      next if old_product.collection.nil?
      puts 'coll'
      cat_map = CategoryMap.where(cat_type: old_product.collection.coltype).first
      next if cat_map.nil?
      product.cat_type = cat_map.name.split('/').map { |a| prepare_string a }.take(3).to_json
      puts product.cat_type
      product.save!
    end
  end

  def self.update_lala_style_prices
    ProductNew.includes(:purchase).where(purchase_id: 83).find_each do |product|
      colors=JSON.parse(product.colors)

      colors.each do |c, json|
        json['sizes'].each do |size, size_json|
          price = size_json['stock_price'].to_f
          if json['sizes'][size]['retail_price'].nil?
            json['sizes'][size]['retail_price'] = (price*1.7).ceil
          end
        end
      end

      product.colors = colors.to_json
      product.save!
    end
  end

  def self.update_producers
    ProductNew.includes(:purchase).where(purchase_id: 83).find_each do |product|
      product.producer = 'Россия'
      product.save!
    end
    ProductNew.includes(:purchase).where(purchase_id: 13).find_each do |product|
      product.producer = 'Россия'
      product.save!
    end
  end

  def self.create_category_map(catmap)
    CSV.foreach(catmap, :col_sep => ';', :headers => false, :encoding => 'Windows-1251:utf-8') do |fields|
      map=CategoryMap.new
      map.name = fields[0]
      map.cat_type = fields[1]
      map.save!
    end
  end

  def self.create_new_category_map(catmap)
    CSV.foreach(catmap, :col_sep => ',', :headers => true, :encoding => 'Windows-1251:utf-8') do |fields|
      id = fields[0]
      name = (1..5).map{|i|fields[i]}.take_while{|f| not f.blank?}.join ' / '
      puts name, fields[3].blank?, fields[3].nil?
      existing = CategoryMap.find_by_cat_type(id)
      if existing.nil?
        map=CategoryMap.new
        map.name = name
        map.cat_type = id
        map.save!
      elsif existing.name != name
        existing.name = name
        existing.save!
      end
    end
  end

  def self.add_sizes_table
    lala_style = [
        ['Размер', '42', '44', '46', '48', '50', '52', '54', '56', '58', '60'],
        ['Рост', '170', '170', '170', '170', '170', '170', '170', '170', '170', '170'],
        ['Обхват груди', '84', '88', '92', '96', '100', '104', '108', '112', '116', '120'],
        ['Обхват бедер', '92', '96', '100', '104', '108', '112', '116', '120', '124', '128'],
        ['Обхват талии', '66', '70', '74', '78', '82', '86', '90', '94', '98', '102'],
    ]

    hochu_platye_women_table = [
        ['Размер', '42', '44', '46', '48', '50', '52', '54', '56'],
        ['Обхват груди', '84', '88', '92', '96', '100', '104', '110', '116'],
        ['Обхват талии', '66', '70', '74', '78', '82', '86', '92', '98'],
        ['Обхват бедер', '90', '94', '98', '102', '106', '110', '116', '122']
    ]
    hochu_platye_child_table = [
        ['Рост', '92', '98', '104', '110', '116'],
        ['Обхват груди', '51-53', '52-54', '53-55', '54-58', '57-60'],
        ['Обхват талии', '50-52', '50-52', '51-53', '52-54', '53-55'],
        ['Обхват бедер', '54-56', '56-58', '58-60', '60-62', '61-63'],
        ['Возраст', '2-3 года', '3-4 года', '4-5 лет', '5-6 лет', '6-7 лет']
    ]
    json = lala_style.to_json
    ProductNew.includes(:purchase).where(purchase_id: 83).find_each do |product|
      product.sizes_table = json
      product.save!
    end
    ProductNew.includes(:purchase).where(purchase_id: 13).find_each do |product|

      is_child = product.cat_type.downcase.to_s.include? 'детям'

      product.sizes_table = is_child ? hochu_platye_child_table.to_json : hochu_platye_women_table.to_json
      product.save!
    end
  end

  def self.update_my_print_bar_prices
    config = MyPrintBar::MyPrintBarProductConfig.new
    ProductNew.includes(:purchase).where(purchase_id:84).find_each do |product|
      puts product.purchase_id
      puts product.sku

      price = config.get_price_by_category(product.cat)
      puts price
      next if price == 0

      colors=JSON.parse(product.colors)
      colors.each do |c, json|
        json['sizes'].each { |size, size_json|
          json['sizes'][size]['price'] = get_price(price, 1.28)
          json['sizes'][size]['stock_price'] = price
          json['sizes'][size]['retail_price'] = get_price(price, 1.7, true)
        }
      end

      product.colors = colors.to_json
      product.save!

    end

    Product.joins(:collection).where(collections: {purchase_id: 84}).find_each do |product|
      puts product.art

      price = config.get_price_by_category(product.collection.name)
      puts price
      next if price == 0
      product.price = price
      product.save!
    end
  end

  def self.update_okean_categories
    ProductNew.includes(:purchase).where(purchase_id:103).find_each do |product|
      cat_type = JSON.parse(product.cat_type)
      if cat_type[0] == 'Обувь' and (cat_type[1].include?('мальчиков') or cat_type[1].include?('девочек'))
        cat_type[1] = 'Детская'
      end
      if cat_type.length == 2
        cat_type[1] = 'Мужская' if product.cat.include? 'Мужская'

        last_category = prepare_string product.cat.gsub(cat_type.join(', ') + ', ', '')
        cat_type << last_category
      end

      product.cat_type = cat_type.to_json

      puts product.cat_type

      product.save!
    end
  end

  def self.update_bimki_categories
    ProductNew.includes(:purchase).where(purchase_id:110).find_each do |product|
      cat_type = JSON.parse(product.cat_type)

      if cat_type.length == 2
        cat_type = ['Детям', 'Для новорожденных', prepare_string(product.cat.gsub('Для новорожденных, ', ''))] if product.cat.include? 'новорожденных'

        cat_type << 'РАСПРОДАЖА' if product.cat.include? 'РАСПРОДАЖА'

        product.cat = prepare_string(product.cat.gsub(' - Скидки', '')) if product.cat.include? 'РАСПРОДАЖА'

        cat_type = ['Детям' , 'Для мальчиков', 'Одежда'] if product.cat.include? 'мальчиков'
        cat_type = ['Детям' , 'Для девочек', 'Одежда'] if product.cat.include? 'девочек'
      end

      if cat_type[1] == 'Школа'
        cat_type[1], cat_type[2] = cat_type[2], cat_type[1]
        cat_type[1] = 'Для девочек' if cat_type[1].include? 'девочек'
        cat_type[1] = 'Для мальчиков' if cat_type[1].include? 'мальчиков'
      end

      product.cat_type = cat_type.to_json

      puts product.cat_type

      product.save!
    end
  end

  def self.update_lala_style_categories
    ProductNew.includes(:purchase).where(purchase_id:83).find_each do |product|
      cat_type = JSON.parse(product.cat_type)

      if product.cat.include? 'из шерсти' and cat_type.length == 2
        cat_type << 'Трикотаж'
      end

      product.cat_type = cat_type.to_json

      puts product.cat_type

      product.save!
    end
  end

  def self.update_okean_low_prices
    ProductNew.includes(:purchase).where(purchase_id:103).find_each do |product|
      puts product.sku, product.name
      colors=JSON.parse(product.colors)
      colors.each do |c, json|
        json['sizes'].each { |size, size_json|
          price = json['sizes'][size]['price']
          json['sizes'][size]['retail_price'] = json['sizes'][size]['retail_price'] + 100 if price < 200
          json['sizes'][size]['price'] = price + 100 if price < 200
        }
      end

      product.colors = colors.to_json
      product.save!
    end
  end

  def self.remove_bimki_categories
    ProductNew.includes(:purchase).where(purchase_id:112).find_each do |product|
      category_lower = product.cat.downcase.to_s
      product.destroy if category_lower.include? 'комплект'
    end

    Collection.where(purchase_id:112).find_each do |collection|
      category_lower = collection.name.downcase.to_s
      collection.destroy if category_lower.include? 'комплект'
    end
  end

  def self.get_ivanovo_modifiers(ivanovo, quantity, folders=[], names=[])
    result = 0
    all_length = 0
    folder_length = 0
    CSV.foreach(ivanovo, :col_sep => ';', :headers => true, :encoding => 'utf-8') do |fields|
      next if fields['Остаток'].to_i <= 0
      next if fields[-1].nil?
      all_length+=1

      puts fields
      is_quantity = fields['Остаток'].to_i <= quantity
      is_folder = folders.empty?
      is_folder = folders.any? {|f| fields[-1] == f} unless is_folder

      is_name = names.empty?
      is_name = names.any? {|f| fields[0].include? f} unless is_name

      result+=1 if is_quantity and is_folder and is_name
      folder_length+=1 if is_folder and is_name
    end
    puts "#{result}, #{folder_length}, #{all_length}, #{result.to_f / all_length * 100}, #{result.to_f / folder_length * 100}"
    result
  end
end

