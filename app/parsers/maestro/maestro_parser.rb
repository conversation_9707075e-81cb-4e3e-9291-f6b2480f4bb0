#encoding: utf-8
module Maestro
  class MaestroParser < Downloader

    @dlparams = {'login' => :text, 'password' => :password}

    def initialize
      @dlparams = {'login' => :text, 'password' => :password}

      @added=[]
      super
    end

    def get_new_category_type(cat)
      ['Продукты питания']
    end


    def process_page(url)
      @log.info(url)

      page = @agent.get(url)

      return if page.search('span.sale_no').count>0

      @purchase.message="Загрузка #{url}"
      @purchase.save

      art=page.search('.item_article span').text.gsub('EAN ','')

      code=url.split('/')[-2..-1].join('/')

      cat=page.search('.bx-breadcrumb .bx-breadcrumb-item span')[1].text

      price=page.search('.item_price span').first.text.to_i

      name=page.search('h1').text.strip

      desc=page.search('#element .col-xs-60')[1].search('div').first.text+page.search('#element .col-xs-60')[1].search('div')[1].text

      desc.gsub!(/\s+/,' ')
      desc.strip!

      pic_urls=page.search('.carousel-inner a').map {|a| a.attr('href'.to_s)}

      pics=[]
      pic_urls.each_with_index do |pic_url, i|
        savePic(pic_url,"#{art}~#{i+1}",true)
        pics<<"#{art}~#{i+1}"
      end

      col=addCollection(cat,'4695')
      addProduct(col,code+' '+art,name,price,desc,[],pics)

    end


    def process_cat(href)
      #puts "Cat #{href}"
      page = @agent.get href

      page.search('.items_wrap .item_name a').each do |a|
        process_page(a.attr('href').to_s)
      end
    end


    def run
      if @agent == nil then
        before
      end

      page = @agent.get('http://maestrotoys.ru/')

      ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest', 'Accept' => 'application/json, text/javascript, */*'}

      form_data={email: @login, password: @password, remember: 'Y'}
      @agent.post('http://maestrotoys.ru/api/login/',form_data,ajax_headers)

      page = @agent.get('http://maestrotoys.ru/catalog/')

      page.search('#left_sidebar a.item_name').each {|a|
          process_cat(a.attr('href').to_s)
      }

    end

  end
end
