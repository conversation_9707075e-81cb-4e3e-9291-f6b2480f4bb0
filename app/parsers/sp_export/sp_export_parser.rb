module SpExport
  class SpExportParser < Downloader
    include SpAuthorize

    def initialize
      @pid = nil
      @products=[]
      super
    end

    def process_src_col(pid,cid)
      @current_user.company.send_ws_notification({task: 'export_purchase', pid:@pid,msg: "Считывается коллекция #{cid}"})

      page = @agent.get("https://www.100sp.ru/org/collection/edit/#{cid}")

      col_name=page.search('#collection-edit .editForm  h3:not(.page-header) input').first.attr('value').to_s.gsub("\u00A0", "").strip.gsub(/ - [0-9]+$/,'').gsub(/ +/,' ').gsub('Опубликована','').strip
      puts "'#{col_name}'"

      if page.search('.collectionEditPanel .infoBlock a').first
        cat_id=page.search('.collectionEditPanel .infoBlock a').first.attr('href').to_s.gsub(/\D/,'')
      end

      puts cat_id

      #if @include_soldout
        selector='li.goods-item'
      #else
      #        selector='li.goods-item:not(.muted)'
      #end

      page.search(selector).each do |li|
        gid=li.attr('data-gid').to_s
        next if gid=='0'

        sku=li.search('input[data-type=articul]').first.attr('value').to_s
        name=li.search('input[data-type=name]').first.attr('value').to_s
        next if sku.blank? and name.blank?

        price=li.search('input[data-type=price]').first.attr('value').to_s
        sizes=li.search('input[data-type=sizes]').first.attr('value').to_s
        source=li.search('input[data-type=source]').first.attr('value').to_s
        desc=li.search('textarea[data-type=description]').first.text.to_s.strip
        rrp=''
        rrp=li.search('input[data-type=recommendedPrice]').first.attr('value').to_s if li.search('input[data-type=recommendedPrice]').first

        if li.attr('class').to_s.include? 'muted'
          sizes=sizes.split(',').map {|s| (s1,r)=s.gsub(/@.*/,'')+'@0'}.join(',')
        end
        brand_name=nil

        if li.at('select[data-type=brand-tag] option[selected]')
          brand_name=li.at('select[data-type=brand-tag] option[selected]').text.to_s
        end

        if brand_name.nil? and li.at('button.goods-brand-tag--link') and li.at('button.goods-brand-tag--link').text.strip!='Укажите бренд'
          brand_name=li.at('button.goods-brand-tag--link').text.strip
        end

        name.gsub!(/[\r\n]/,' ')
        name.gsub!(/ +/,' ')

        desc.gsub!(/[\r\n]/,' ')
        desc.gsub!(/\.+/,'.')
        desc.gsub!(/(\. )+/,'. ')
        desc.gsub!(/ +/,' ')
        desc.gsub!(/^\.+/,'')


        pics=JSON.parse(li.attr('data-pics').to_s).map {|el| el['originalurl']}

        good_cat_id=li.attr('data-category').to_s

        cat_id=good_cat_id if cat_id.nil? and good_cat_id.to_s!=''

        good_cat_id=cat_id if good_cat_id.to_s=='' and cat_id

        group = @groups[cid.to_i]

        @products<<{col_name:col_name,sku:sku,name:name,price:price,rrp:rrp,sizes:sizes,source:source,desc:desc,pics:pics,cat_id:good_cat_id,brand_name:brand_name,gid:gid, group:group}

      end


      sleep(1+rand(0.0..0.4))
  #
    end

    def get_groups(page)
      ret = {}
      if /data-groups='(.*?)'/ =~ page.body
        d1 = JSON.parse($1)
        /data-groups-reference='(.*?)'/ =~ page.body
        d2 = JSON.parse($1)
        d2.each do |d|
          ret[d['cid'].to_i] = d1.find { |t| t['id'] == d['collection_group_id'] }['name']
        end
      end
      ret
    end

    def read_source(pid)
      page = @agent.get("https://www.100sp.ru/org/collection/list/#{pid}")

      @groups = get_groups(page)

      page.search(".purchase-collection-list > .rows  > div").each do |d|
        cid=d.attr('id').to_s
        next if cid.to_i==0

        puts cid
        process_src_col(pid,cid)
      end


    end


    def run(pid, current_user)
      @pid = pid
      @current_user = current_user

      authorize

      @col_map_goods=Hash.new

      read_source @pid

      workbook = RubyXL::Workbook.new
      sheet = workbook.worksheets[0]

      line=['Коллекция','Артикул','Название','Подробнее','Цена','РРЦ','Размеры','UUID','Источник товара','Категория','Бренд','Группа коллекции','gid','Картинка','Картинка 1','Картинка 2','Картинка 3','Картинка 4','Картинка 5','Картинка 6']
      line.each_with_index { |d,k| sheet.add_cell(0,k,d) }

      @products.each_with_index do |p,i|
        line=[p[:col_name],p[:sku],p[:name],p[:desc],p[:price],p[:rrp],p[:sizes],'',p[:source],p[:cat_id],p[:brand_name].to_s,p[:group],p[:gid]]+p[:pics]

        line.each_with_index { |d,k| sheet.add_cell(i+1,k,d) }
      end

      fn=Rails.root.join('files', "export_#{@pid}.xlsx").to_s
      workbook.write(fn)

      current_user.company.send_ws_notification({task: 'export_purchase', pid:@pid, status: 'done', filename:"export_#{pid}.xlsx", data: Base64.encode64(File.read(file))})
    end
  end
end
