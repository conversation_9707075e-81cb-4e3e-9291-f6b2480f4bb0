# encoding:utf-8

module Plusminus
  class PlusminusOrderSender < Framework::OrderSender
    def initialize
      super
      @needed_cookies = ['s3_user_token', 's3s2_cart']
    end

    def authorize
    end

    def clean
      super
    end

    def search_by_sku(sku)
      retry_count = 2
      page = @agent.get("https://ochki-plusminus.ru/my/s3/xapi/public/?method=shop2/getProductsBySearchMatches&param[s][search_text]=#{sku}")
      sleep(0.6 + rand(0.5))
      d = JSON.parse(page.body)
      if d['result']['success']
        html = d['result']['html']
        doc = Nokogiri::HTML(html)
        return doc.at('.product-item__top .gr-product-image a[href]')&.attr('href')
      end
      nil
    rescue Mechanize::ResponseCodeError => exception
      puts 'retry'
      @log.info exception
      @log.info exception.page.body
      if exception.response_code == '500' or exception.response_code == '403'
        puts '500/403'
        sleep(30)
        retry_count -= 1
        retry if retry_count >= 0
      end

    end

    def get_sizes(page)
      if page.at('select[name=cf_cml_dioptria_2878504466]')
        page.search('select[name=cf_cml_dioptria_2878504466] option').map { |o| [o.text, o.attr('data-kinds')] }.to_h
      else
        nil
      end
    end

    def order_kind(kind, q, hash, ver_id)
      retry_count = 2
      puts "#{kind} #{q}"
      data = { hash: hash, ver_id: ver_id, kind_id: kind, amount: q }
      page = @agent.post('https://ochki-plusminus.ru/my/s3/api/shop2/?cmd=cartAddItem', data, @ajax_headers)
      puts page.body
      sleep(0.6 + rand(0.5))
    rescue Errno::ECONNRESET
      puts 'retry'
      sleep(30)
      retry_count -= 1
      retry if retry_count >= 0
    rescue Mechanize::ResponseCodeError => exception
      puts 'retry'
      @log.info exception
      @log.info exception.page.body
      if exception.response_code == '500' or exception.response_code == '403'
        puts '500/403'
        sleep(30)
        retry_count -= 1
        retry if retry_count >= 0
      end

    end

    def order(sku, sizes)
      puts sku
      url = search_by_sku(sku)
      puts url
      return unless url

      retry_count = 2
      begin
        page = @agent.get url
        sleep(0.6 + rand(0.5))
      rescue Mechanize::ResponseCodeError => exception
        puts 'retry'
        @log.info exception
        @log.info exception.page.body
        if exception.response_code == '500' or exception.response_code == '403'
          puts '500/403'
          sleep(30)
          retry_count -= 1
          retry if retry_count >= 0
        end
      end

      product_id = page.at('input[name=product_id]').attr('value').to_s
      puts product_id

      if /shop2.init\((.*?)\);shop2.facets.enabled/ =~ page.body
        d = JSON.parse($1)
        # page_sku = d['productRefs'][product_id]['cml_kod_1378315570'].keys[0]
      end

      return unless d
      # return unless page_sku == sku

      hash = d['apiHash']['cartAddItem']
      ver_id = d['verId']
      kind_id = page.at('input[name=kind_id]').attr('value').to_s

      size_data = get_sizes(page)

      sizes.each do |size, q|
        if size == '-'
          order_kind(kind_id, q, hash, ver_id)
        else
          if size_data&.include? size
            order_kind(size_data[size], q, hash, ver_id)
          else
            add_error_csv_data('Size not found', url, size)
          end
        end
      end
    end

    def add_csv_data(fields)
      sku = fields['Артикул'].gsub(/ .+/, '')
      size = fields['Размер'].to_s

      @csv_data[sku] = {} unless @csv_data.include? sku

      @csv_data[sku][size] += 1 if @csv_data[sku].include? size
      @csv_data[sku][size] = 1 unless @csv_data[sku].include? size
    end
  end
end
