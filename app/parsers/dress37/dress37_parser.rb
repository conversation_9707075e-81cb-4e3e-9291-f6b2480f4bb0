module Dress37
  class Dress37Parser < Downloader
    include InvoiceFullnameSize
    def initialize
      @dlparams={}

      super
    end

    def process_page(url)
      tries ||= 2

      @log.info(url)
      page = @agent.get(url)
      puts url


      name=page.search('h1.product_title.entry-title').first.text.to_s.strip
      art=URI::Parser.new.unescape(url.split('/').last)

      cat=page.search('#online-shop-breadcrumbs a').last.text.to_s
      cat="Для мужчин - #{cat}" if page.search('#online-shop-breadcrumbs a')[1].text.to_s.include? 'Муж'


      price=page.search('span.woocommerce-Price-amount.amount').first.text.gsub(',','.').gsub(/[^0-9.]/,'')
      rrp=(price.to_f*1.7).ceil

      desc=page.search('#tab-description p').first.text.gsub(/ +/,' ').strip if page.search('#tab-description p').first
      desc=page.search('#tab-description div').first.text.gsub(/ +/,' ').strip if desc.nil? and page.search('#tab-description div').first



      short_desc=page.search('.woocommerce-product-details__short-description').text
      if /(Состав: .*)/=~short_desc
        desc="#{desc}. #{$1}."
      end
      if /(Материал: .*)/=~short_desc
        desc="#{desc}. #{$1}."
      end

      desc.gsub!(/\.+/,'.')
      desc.gsub!(/ +/,' ')

      sizes=page.search('td.awspc-field-label-line').map {|td| td.text.gsub('Размер','').strip}

      return if sizes.count==0

      pic_urls=page.search('div[data-thumb] a').map{|a| a.attr('href').to_s}
      pics=[]
      pic_urls.each_with_index {|url,i|
        pics<<savePic(url,"#{art}~#{i}",true)
      }

      cat_type=guess_cat_type(cat,name,850)

      col=addCollection(cat,cat_type)
      p = addProduct(col,art,name,price,desc,sizes,pics,nil,0,nil,rrp:rrp) #unless @products.include? art
      unless p.weight
        p.weight = ChatGpt.new.get_product_weight(p.name)
        p.save
      end

      # if page.search('span.old-price').count>0 or page.search('.action-label-product b').count>0 and cat!='СКИДКИ'
      #   col=addCollection('СКИДКИ','850')
      #   prices.each {|price, sizes|
      #     addProduct(col,art,name,price,desc,sizes,pics)
      #   }
      # end



    rescue Mechanize::Error => e
      puts e
      print e.backtrace.join("\n")
      retry unless (tries-=1).zero?

    end

    def process_cat(href)
      @log.info "Process cat #{href}"

      page=@agent.get(href)

      page.search('a.woocommerce-loop-product__link').each {|a|
        process_page(a.attr('href').to_s)
      }

      if page.search('a.next.page-numbers').count>0
        process_cat page.search('a.next.page-numbers').first.attr('href').to_s
      end
    end

    def run
      page=@agent.get 'http://dress37.ru/'

      page.search(".acmethemes-nav ul.sub-menu > li a").each {|a|
        process_cat(a.attr('href').to_s+'?limit=all')
      }
  end
  end
end
