#encoding:utf-8
require_relative '../framework/site/product_config'
require_relative 'bimki_utilities'


module Bimki
  class BimkiProductConfig < Framework::Site::ProductConfig
    def get_page(link)
      begin
        @visited=[] if @visited.nil?

        return if @visited.include? link
        @visited << link

        sleep(0.3 + rand()*0.3)
        result = @agent.get(link)
        if Bimki::BimkiUtilities.get_price(result).nil?
          @log_func.call "Has exception on #{link}, restart agent"
          @agent.shutdown
          sleep(7.minutes)
          @agent = @create_agent_func.call
          @agent.request_headers
          link = "https://bimki.ru/#{link}" unless link.include? 'http'
          result = @agent.get(link)
          return nil if Bimki::BimkiUtilities.get_price(result).nil?
          return result
        else
          return result
        end
      rescue Mechanize::ResponseCodeError => exception
        if exception.response_code.to_i >= 500
          @log_func.call "Has exception on #{link}, restart agent"
          @agent.shutdown
          sleep(7.minutes)
          @agent = @create_agent_func.call
          @agent.request_headers
          retry
        else
          raise # Some other error, re-raise
        end
      end
    end

    def get_new_category_type(category, name)
      category_lower = category.downcase.to_s
      name_lower = name.downcase.to_s

      return ['Детям', 'Для девочек', 'РАСПРОДАЖА'] if category_lower.include? 'распродажа' and category_lower.include? 'девочкам'

      if not name_lower.include? 'комплект' or not category_lower.include? 'комплект'
        result = super(get_category_type(category, name))

        if result.length == 2
          result = ['Детям', 'Для мальчиков', 'Одежда'] if category_lower.include? 'мальчикам'
          result = ['Детям', 'Для девочек', 'Одежда'] if category_lower.include? 'девочкам'
        end

        if result[0].include? 'Спорт'
          result[1] = 'Для мальчиков' if category_lower.include? 'мальчикам'
          result[1] = 'Для девочек' if category_lower.include? 'девочкам'
        end

        return result
      end

      return ['Детям', 'Для новорожденных', 'Комплекты одежды'] if category_lower.include? 'малышам'
      return ['Детям', 'Для мальчиков', 'Комплекты одежды'] if category_lower.include? 'мальчикам'

      ['Детям', 'Для девочек', 'Комплекты одежды'] if category_lower.include? 'девочкам'
    end

    def get_category(name, is_sale=false, is_new=false)
      name_mb = name.downcase.to_s

      result = ''
      # result = 'комплект'
      result = 'Девочкам, Юбки' if name_mb.include? 'юбка'
      result = 'Девочкам, Джемперы, кофты' if name_mb.include? 'джемпер'
      result = 'Девочкам, Белье' if name_mb.include? 'колготки'
      result = 'Осень-зима, Мальчикам, Рубашки-поло' if name_mb.include? 'рубашка-поло'
      result = 'Мальчикам, Рубашки' if name_mb.include? 'рубашк'
      result = 'Девочкам, Платья' if name_mb.include? 'платье'
      result = 'Девочкам, Пижамы' if name_mb.include? 'пижама'
      result = 'Девочкам, Блузки' if name_mb.include? 'блузка'
      result = 'Девочкам, Белье' if name_mb.include? 'майка'
      result = 'Девочкам, Брюки, джинсы, штаны' if name_mb.include? 'джинсы'
      result = 'Девочкам, Сарафан' if name_mb.include? 'сарафан'
      result = 'Девочкам, Шапки, головные уборы' if name_mb.include? 'шапка'
      result = 'Девочкам, Жакеты, пиджаки' if name_mb.include? 'пиджак'
      result = 'Малышам, Комбинезоны' if name_mb.include? 'комбинезон'
      result = 'Девочкам, Футболки, майки, топы' if name_mb.include? 'футболка'
      result = 'Девочкам, Брюки, джинсы, штаны' if name_mb.include? 'брюки'
      result = 'Девочкам, Жакеты, пиджаки' if name_mb.include? 'жилет'
      result = 'Девочкам, Шорты' if name_mb.include? 'шорты'
      result = 'Девочкам, Футболки, майки, топы' if name_mb.include? 'топ'
      result = 'Осень-зима, Девочкам, Куртки, ветровки, плащи, пальто' if name_mb.include? 'ветровка'
      result = 'Девочкам, Жакеты, пиджаки' if name_mb.include? 'жакет'
      result = 'Девочкам, Белье' if name_mb.include? 'трусы'
      result = 'Малышам, Штанишки, брюки, джинсы' if name_mb.include? 'штанишки'
      result = 'Малышам, Ползунки' if name_mb.include? 'ползунки'
      result = 'Осень-зима, Девочкам, Куртки, ветровки, плащи, пальто' if name_mb.include? 'куртка'
      result = 'Девочкам, Водолазки' if name_mb.include? 'водолазк'
      result = 'Осень-зима, Девочкам, Куртки, ветровки, плащи, пальто' if name_mb.include? 'пальто'
      result = 'Нарядная, Девочкам, Болеро' if name_mb.include? 'болеро'
      result = 'Девочкам, Бриджи' if name_mb.include? 'бриджи'
      result = 'Малышам, Боди' if name_mb.include? 'боди'
      result = 'Девочкам, Джемперы, кофты' if name_mb.include? 'кофта'
      result = 'Девочкам, Джемперы, кофты' if name_mb.include? 'кардиган'
      result = 'Девочкам, Носки, колготки' if name_mb.include? 'носки'
      result = 'Осень-зима, Девочкам, Шапки, шарфы, варежки' if name_mb.include? 'варежки'
      result = 'Девочкам, Белье' if name_mb.include? 'косынка'
      result = 'Обувь, Девочкам' if name_mb.include? 'туфли'
      result = 'Обувь, Девочкам' if name_mb.include? 'чешки'
      result = 'Девочкам, Джемперы, кофты' if name_mb.include? 'свитер'
      if result.blank?
        return "Распродажа" if is_sale
        return "Новинки" if is_new
        return 'Все товары'
      end

      result = "#{result} - Распродажа" if is_sale
      result = "#{result} - Новинки" if is_new
      result
    end

    def parse_product(product_page, link=nil)
      return nil if product_page.nil?

      result = super(product_page, link)
      result.link = result.link.split('&prev_url').first
      if result.category.include?('Распродажа')
        result.category = get_category(result.name, true)
      elsif result.category.include?('Новинки')
        result.category = get_category(result.name, false, true)
      elsif result.category.include?('Все товары')
        result.category = get_category(result.name)
      end

      return nil if result.category.downcase.to_s.include? 'комплект'
      return nil if result.name.downcase.to_s.include? 'комплект'

      result
    end

    def create_new_product(product_page, link=nil)
      @purchase.check_stop
      product = parse_product(product_page, link)

      puts 'parse product'

      return if product.nil?

      puts 'parse colors'
      colors = Bimki::BimkiUtilities.get_available_colors(product_page)

      return if colors.nil?

      puts 'add colors'
      json = {}

      colors.each do |color_name, color_object|
        next if color_object['sizes'].length == 0

        clone_product = product.clone
        clone_product.name = "#{clone_product.name} Цвет: #{color_name}"
        clone_product.description = "Цвет: #{color_name}. #{clone_product.description}"
        clone_product.sizes = color_object['sizes'].find_all{|s| not s['disabled']}.map {|s| s['text']}

        add_product clone_product

        get_json(clone_product, color_name).each { |key, value| json[key] = value }
      end
      cat_type = get_new_category_type(product.category, product.name)
      add_product_new product, json, cat_type
    end

    def with_create_new_agent_func(create_agent_func)
      @create_agent_func = create_agent_func
      self
    end
  end
end