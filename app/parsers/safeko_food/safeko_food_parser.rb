require 'creek'

module SafekoFood
  class SafekoFoodParser < Downloader
    attr_accessor :price_file

    def initialize
      @price_file=''
      @dlparams={'price_file'=>:file}

      super
    end


    def process_file(file)
      creek = Creek::Book.new @price_file
      sheet = creek.sheets[0]

      file_pics={}
      sheet.with_images.rows.each do |row|
        next if row.values[2].nil?
        barcode=row.values[2].to_s.gsub(/\.0$/,'').gsub(/\D/,'')
        puts barcode
        if row.values[1].is_a? Array
          file_pics[barcode]=row.values[1][0]
        end

      end

      cat_name=nil
      sheet.rows_with_meta_data.each do |row|
        next if row['cells'].values[2]=='Код'

        if row['cells'].values[8].nil?
          if row['cells'].values[1]!=nil and row['cells'].values[1].is_a?(String)
            cat_name=name=row['cells'].values[1]
            cat_name.strip!
          end
          next
        end

        code=row['cells'].values[2].to_s.gsub(/\.0$/,'').gsub(/\D/,'')
        art=row['cells'].values[4].to_s.gsub(/\.0$/,'')
        puts art

        name=row['cells'].values[5].gsub(%r!/\d+$!,'').strip
        name.gsub!(art,'')
        name.gsub!(/гр\.(\S)/,'гр. \1')
        name.gsub!(/\*(\d+) шт/ ,'*\1шт')

        name.strip!

        suff=name.split.last
        name.gsub!(suff,'') if suff.include? '*' and suff.include? 'шт'

        name.gsub!(/,$/,'')

        name.gsub!(/\s+/,' ')
        puts name


        desc=''

        price=(row['cells'].values[9].to_f*1.2).ceil

        name_d=name.downcase

        cat_type=100350

        code=art if code.blank?

        col=addCollection(cat_name,cat_type)
        prod=addProduct(col,code,name,price,desc,[],[])

        i=Picture.where(product_id: prod.id, image_type: :sample_pic).first
        unless i
          pic=savePic(file_pics[code],code,true,false,true,:sample_pic)
          prod.pictures<<pic if pic
          prod.save
        end

      end
    end

    def run
      if @agent==nil then before end

      process_file(@price_file)

    end

  end
end
