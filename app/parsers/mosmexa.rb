#encoding: utf-8

require 'downloader'
require 'spreadsheet'

class Mosmexa < Downloader
  attr_accessor :price_file

  def initialize
    @price_file=''
    @dlparams={'price_file'=>:file,'previous_purchase'=>:text}

    super
  end

  def process_page(url)
    puts url
    page = @agent.get url

    art=page.search('span#product_artikul').text.gsub('Артикул:','').strip.to_i
    puts art

    name=page.search('.big_page_title h1').text.strip

    if page.search('.breadcrumb a').count==4
      cat=page.search('.breadcrumb a')[1].text.strip+'-'+page.search('.breadcrumb a')[-1].text.strip
    else
      cat=page.search('.breadcrumb a')[1].text.strip+'-'+page.search('.breadcrumb a')[2].text.strip
    end

    urls=page.search('ul.thumbs_carousel a').map {|a| a.attr('href').to_s}
    desc=page.search('.product_item_description').text

    titles=page.search('td.attribut-name').map {|td| td.text.strip}
    vals=page.search('td.attribut-value').map {|td| td.text.strip}
    props=Hash[titles.zip vals]
    props.delete_if { |key, value| key.include? 'Артикул'}
    desc2=props.map {|key, value| "#{key} #{value}"}.join '. '
    desc=desc2+'. '+desc

    desc.gsub!("\r",' ')
    desc.gsub!("\n",' ')
    desc.gsub!(' ',' ')
    desc.gsub!(/ +/,' ')

    sizes=page.search('#choose_size .select_size .size_list label').map {|l| l.text.strip}

    return if not @prices.has_key? art

    puts urls
    puts desc
    puts sizes
    puts @prices[art]

    pics=[]

    urls.each_with_index  {|url,i|
      savePic(url,"#{art}~#{i}",false)
      pics<<"#{art}~#{i}"
    }

    cat_type='1675'
    cat_type='1541' if cat.include? 'Пух'

    cat_type='2440' if cat.include? 'Муж'
    cat_type='916' if cat.include? 'Муж' and cat.include? 'Пух'

    col=addCollection(cat,cat_type)
    addProduct(col,art,name,@prices[art],desc,sizes,pics)
  end

  def process_cat(url)
    puts url
    page = @agent.get url

    page.search('.cat_item .imgcat >a:not(.quick_v)').each do |a|
      process_page a.attr('href').to_s
    end
  end

  def read_file(f)
    book = Spreadsheet.open f

    sheet = book.worksheets[0]
    start_row=0
    sheet.rows.each_with_index do |row,i|
      next if row[2].nil?
      art=row[2].to_i
      price=row[3]
      puts art
      puts price
      @prices[art]=price
    end
  end



  def run
    if @agent==nil then before end
    @prices=Hash.new

    read_file @price_file

    page = @agent.get('http://www.mosmexa.ru/')
    page.search('ul.nav .nav_dropdown')[0..1].each do |div|
      div.search('li.bold a').each do |a|
        process_cat a.attr('href').to_s+'?show=all'
      end
    end
  end


end