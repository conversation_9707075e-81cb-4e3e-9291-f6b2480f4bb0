#encoding: utf-8
module Belsavon
  class BelsavonParser < Downloader
    include InvoiceFullname

    @dlparams = {}

    def initialize
      @dlparams = {}

      @prices = {}
      @sizes = {}
      super
    end



    def process_page(url)
      @log.info(url)
      page = @agent.get(url)

      cat=page.at('.jshop_menu_level_0_a a').text.gsub('оптом','').strip

      art=url.split('/')[-2..-1].join('/')

      name=page.search('form[name=product] h1').first.text.strip.gsub(/, \d+ шт.*/,'')

      desc=page.search('#vina-description').text.gsub(/\s+/,' ').strip
      desc2=page.search('.jshop_prod_description').text.gsub('Краткое описание','').gsub(/\s+/,' ').strip

      desc="#{desc2}. #{desc}".gsub('..','.')

      price=page.search('span#block_price').text.to_f

      if /Цена указана за (\d+) штук/=~desc
        price/=$1.to_f
        desc.gsub!(/Цена указана за .*/m,'')
      end

      desc.gsub!('Больше фотографий >>','')
      desc.gsub!(/\.+/,'.')

      return if price<5

      pic_urls=page.search('a[id^="main_image_full"]').map {|a| a.attr('href').to_s}
      pics=[]
      pic_urls.each_with_index do |url,i|
        pics<<savePic(url,"#{art}~#{i}",true)
      end

      return if pics.empty?

      rrp=(price*1.6).ceil

      col = addCollection(cat, '997')
      addProduct(col, art, name, price, desc, [], pics,nil,0,nil,rrp:rrp)

    rescue Mechanize::Error
    end

    def process_good_list(page,is_sale)
      page.search('.main-content .good-card__title a').each {|a|
        href = a.attr('href').to_s

        process_page(href,is_sale)
      }

    end

    def process_cat(href)
      #puts "Cat #{href}"
      page = @agent.get href

      process_good_list(page,is_sale)

      a = page.search('.pagination__numbers a.page-number')[-2]
      if a
        h = a.attr('href').to_s
        if /(\d+)$/ =~ h
          last_page = $1.to_i
          (2..last_page).each {|p|
            #puts "#{href}?PAGEN_1=#{p}"
            page2 = @agent.get "#{href}?PAGEN_1=#{p}"
            process_good_list(page2,is_sale)
          }
        end
      end
    end


    def run
      if @agent == nil then
        before
      end

      page = @agent.get('https://belsavon.ru/?limit=99999')

      page.search('h3.name a').each {|a|
        process_page(a.attr('href').to_s)
      }
    end

  end
end
