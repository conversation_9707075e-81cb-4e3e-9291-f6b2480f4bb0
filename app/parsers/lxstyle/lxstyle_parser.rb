#encoding: utf-8
module Lxstyle
  class LxstyleParser < Downloader
    include InvoiceLxstyle

    @dlparams = {'login' => :text, 'password' => :password}

    def initialize
      @dlparams = {'login' => :text, 'password' => :password}

      @prices = {}
      @sizes = {}
      @visited=[]
      super
    end


    def process_page(url)
      return if @visited.include? url
      @visited<<url

      @log.info(url)
      page = @agent.get(url)

      pid=page.search('input[name=product_id]').first.attr('value').to_s

      art=page.at('div[itemprop=sku]')
      return if art.nil?

      art=art.text.to_s

      name=page.search('h1[itemprop=name]').first.text.to_s.strip

      cat=page.search('a.Breadcrumbs__Link span')[-2].text.to_s.gsub('>','').strip
      prices={}

      extra=[]

      page.search('ul.skus > li').each do |li|
        disabled = li.at('input[data-disabled=1]')
        next if disabled

        sku_id=li.search('input[name=sku_id]').first.attr('value').to_s
        price=li.search('input[data-price]').first.attr('data-price').to_s
        size=li.search('span').first.text

        #stock=page.search(".sku-#{sku_id}-stock")
        #if stock.count==1 and /Осталось (\d+) штук/=~stock.first.text
        #          size="#{size} (#{$1} шт)"
        #end

        #next if sizes.include? size
        prices[price] = [] unless prices[price]
        prices[price] << size unless prices[price].include? size
        extra<<"#{size}~#{sku_id}"
      end

      #      props={}
      #page.search('table#product-features tr').each do |tr|
      #  k=tr.search('td.name').first.text.strip
      #        v=tr.search('td.value').first.text.strip
      #        props[k]=v
      #end

      #color=props['Цвет']

      desc=page.search('div[itemprop=description]').first
      desc.search('table').remove
      desc=desc.text.to_s

      #desc+=". Ткань: #{props['Ткань']}" if props['Ткань']

      desc.gsub!(/[\r\n]+/, '.')
      desc.gsub!(/\.+ */, '. ')
      desc.gsub!(/ +/, ' ')

      pics=[]

      page.search('a.Single__Thumbnail--Link').each_with_index do |a,i|
        fname="#{pid}~#{i}"
        pics<<savePic(a.attr('href').to_s,fname,true)
      end

      if pics.empty?
        a=page.search('.Single__Link--MainFigure a').first
        return if a.nil?
        pics<<savePic(a.attr('href').to_s,pid.to_s,true)
      end

      cat_type = '850'
      if cat.include? 'ЖЕНСКОЕ'
        cat_type = '102165' if name.include? 'Блуз'
        cat_type = '17' if name.include? 'Брюки'
        cat_type = '102179' if name.include? 'Жакет'
        cat_type = '102180' if name.include? 'Жилет'
        cat_type = '26' if name.include? 'Комбинезон'
        cat_type = '2105' if name.include? 'Комплект'
        cat_type = '102181' if name.include? 'Кофта'
        cat_type = '102186' if name.include? 'Куртка'
        cat_type = '286' if name.include? 'Накидка'
        cat_type = '129' if name.include? 'Пальто'
        cat_type = '1101' if name.include? 'Платье'
        cat_type = '102164' if name.include? 'Рубашка'
        cat_type = '1104' if name.include? 'Сарафан'
        cat_type = '102185' if name.include? 'Свитшот'
        cat_type = '307' if name.include? 'Туника'
        cat_type = '1373' if name.include? 'Футболка'
        cat_type = '19' if name.include? 'Юбка'
        cat_type = '102175' if name.include? 'Шорты'
        cat_type = '102177' if name.include? 'Костюм'
        cat_type = '102165' if name.include? 'Лонгслив'
        cat_type = '102176' if name.include? 'Брюки'
        cat_type = '1373' if name.include? 'Халат'
        cat_type = '3011' if name.include? 'Лосин'
        cat_type = '281' if name.include? 'Бридж'

      elsif cat.include? 'МУЖСКОЕ'
        cat_type = '30' if name.include? 'Футболк'
        cat_type = '30' if name.include? 'Футболк'
        cat_type = '877' if name.include? 'Шорты'
        cat_type = '51' if name.include? 'Костюм'
        cat_type = '1445' if name.include? 'Лонгслив'
        cat_type = '3000' if name.include? 'Брюки'
      elsif cat.include? 'ДЕТСКОЕ'
        cat_type = '870'
        cat_type = '108460' if name.include? 'Футболк'
        cat_type = '108458' if name.include? 'Шорты'
        cat_type = '108464' if name.include? 'Костюм'
        cat_type = '108465' if name.include? 'Брюки'
      end


      cat='РАСПРОДАЖА' if page.search('.badge.low-price').count==1 || cat.include?('РАСПРОДАЖА')

      #name="#{name}. Цвет #{color}" if color

      col = addCollection(cat, cat_type)

      prices.each do |price,sizes|
        rrp=price.to_i.*1.5
        p = addProduct(col, pid+' '+art, name, price, desc, sizes, pics,extra.join('$'),0,nil,rrp:rrp)
        unless p.weight
          p.weight = ChatGpt.new.get_product_weight(p.name)
          p.save
        end
      end
      sleep(0.5)
    rescue Mechanize::Error
    end

    def process_cat(href,is_sale=false)
      return 0 if href.include? 'novinki'

      page = @agent.get(href)

      hrefs = page.search('a.Product__Link--Image')
      hrefs.each do |a|
        process_page(a.attr('href').to_s)
      end
      hrefs.count
    end


    def run
      if @agent == nil then
        before
      end

      page = @agent.get('https://lxstyle.ru/')

      page.search('ul.Nav--Site a.Nav__Link--Root').each {|a|
        cnt=1
        loop do
          break if process_cat(a.attr('href').to_s+"?page=#{cnt}")==0
          cnt+=1
        end

      }
    end

  end
end
