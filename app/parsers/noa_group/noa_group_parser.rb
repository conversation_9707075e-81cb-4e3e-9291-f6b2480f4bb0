#encoding:utf-8
require_relative '../framework/parser'

module NoaGroup
  class NoaGroupParser < Framework::<PERSON><PERSON><PERSON>
    def get_characteristics(product_page)
      titles = product_page.search('.nc_item .title').map { |i| prepare_string i.text }
      values = product_page.search('.nc_item .value').map { |i| prepare_string i.text }
      Hash[titles.zip(values)]
    end

    def get_name(product_page)
      prepare_string product_page.search('h1').first.text
                         .gsub(/остаток \d+ штуки?/i, '')
                         .gsub(/цена(.+)рублей/i, '')
                         .split('.')[0]
                         .split('!')[0]
    end

    def initialize_settings
      with_auth_data(->(ad) {
        ad.with_auth_url('http://www.noagroup.ru/lk/login/')
            .with_login('<EMAIL>')
            .with_password('0DSMyLJwBiH')
            .with_login_field_name('AUTH_USER')
            .with_password_field_name('AUTH_PW')
            .with_form(->(page) {
              page.forms[1]
            })
      })

      with_url('http://www.noagroup.ru/lk/')
          .with_category_links('#menu_left2 li > a')
          .with_category_links('/catalog/bigsize/', true)
          .with_pagination(->(category_page) {
            result = category_page.search('.next_link').first
            return nil if result.nil?
            href = result.attr('href')
            href.include?('javascript') ? nil : href
          })
          .with_category(->(category_page) {
            prepare_string category_page.search('h1').first.text
          })
          .with_product_config(->(pc) {
            pc.with_product_selector('#product_image a.title')
                .with_name(->(product_page) {
                  get_name(product_page)
                })
                .with_articul(->(product_page) {
                  prepare_string product_page.search('#ItemId .value').first.text
                })
                .with_price(->(product_page) {
                  product_page.search('.price').first.text.to_i
                })
                .with_sizes(->(product_page) {
                  sizes = get_characteristics(product_page)['Размерный ряд:'].split('-').map { |s| s.to_i }
                  return ['30', '32', '34', '36'] if sizes.length == 1
                  (sizes[0]..sizes[1]).step(2).to_a.map { |s| s.to_s }
                })
                .with_description(->(product_page) {
                  prepare_string product_page.search('.nc_description > .value').first.text
                })
                .with_images(->(product_page) {
                  images = product_page.search('.big img').map { |img| img.attr('src') }
                  images.concat(product_page.search('.preview img').map { |img| img.attr('src') })
                  images.uniq
                })
                .with_composition(->(product_page) {
                  chars = get_characteristics(product_page)

                  "#{chars['Ткань:']}, #{chars['Состав:']}"
                })
                .with_image_config(->(ic) {
                  ic.with_default_url('http://www.noagroup.ru')
                })
                .with_sizes_table(->(product_page){
                  name_lower = get_name(product_page).downcase.to_s
                  child_arr = ['детск', 'девоч', 'мальч']
                  women_arr = ['женск']
                  men_arr = ['мужск']

                  return get_size_table(0) if child_arr.any?{|c| name_lower.include? c}
                  return get_size_table(1) if women_arr.any?{|c| name_lower.include? c}
                  return get_size_table(2) if men_arr.any?{|c| name_lower.include? c}
                  []
                })
                .with_category_type('850')
                .with_category_type('844', 'детские', 'комплекты')
                .with_category_type('561', 'детские', 'брюки')
                .with_category_type('568', 'детские', 'майки')
                .with_category_type('564', 'детские', 'пижамы')
                .with_category_type('559', 'детские', 'трусы')
                .with_category_type('556', 'детские', 'футболки')
                .with_category_type('556', 'детские', 'фуфайки')
                .with_category_type('281', 'женские', 'бриджи')
                .with_category_type('17', 'женские', 'брюки')
                .with_category_type('850', 'женские', 'майки')
                .with_category_type('850', 'женские', 'пижамы')
                .with_category_type('1373', 'женские', 'футболки')
                .with_category_type('1373', 'женские', 'фуфайки')
                .with_category_type('3011', 'женские', 'халаты')
                .with_category_type('3012', 'комплекты', 'дома')
                .with_category_type('871', 'мужские', 'майки')
                .with_category_type('118', 'мужские', 'пижамы')
                .with_category_type('872', 'мужские', 'трусы')
                .with_category_type('30', 'мужские', 'футболки')
                .with_category_type('30', 'мужские', 'фуфайки')
                .with_category_type('31', 'мужские', 'трико')
                .with_category_type('3013', 'сорочки')
                .with_category_type('20', 'платья')
                .with_category_type('1104', 'сарафаны')
                .with_category_type_by_name('1930', 'больших', 'сорочка')
                .with_category_type_by_name('1930', 'больших', 'пижама')
                .with_category_type_by_name('1930', 'больших', 'комплект')
                .with_category_type_by_name('79', 'больших', 'футболка')
                .with_category_type_by_name('79', 'больших', 'фуфайка')
                .with_category_type_by_name('79', 'больших', 'майка')
          })
    end

    def get_size_table(table_num)
      page = @agent.get('http://www.noagroup.ru/about/size/')
      table = page.search('.nc_row table').to_a[table_num]
      table.search('tr').map{|tr| tr.search('td').map{|td| prepare_string td.text}}
    end
  end
end