#encoding: utf-8

require 'downloader'
#require 'json'
#require 'logger'
#require 'unicode_utils/downcase'
require 'spreadsheet'

class Gulliver < Downloader
  include InvoiceSimple

  attr_accessor :price_file
  attr_accessor :price_file_sale

  def initialize
    @price_file=''
    @dlparams={'price_file'=>:file,'price_file_sale'=>:file,'parse_sites'=>:checkbox}

    @descs=Hash.new('')
    @pics=Hash.new {|hash,key| hash[key]=[] }
    @retail_price=Hash.new
    super
  end


  def proc_art(art,colName,name)
    art2=art.gsub(/\u00a0/, ' ').gsub('Арт.','').strip.gsub('*','').gsub('/','_').strip
    art2=art2.gsub('*', '').gsub('+', '_').gsub('/', '_').gsub("AM-", '').gsub(/Ou$/, '')
    if colName == "WOW" then art2 = art2.gsub("W", "") end
    #if colName.include? "TINY LOVE (Китай)"
    #  if /\((.*?)\)/=~name
    #    art2=$1
    #  end
    #end
    return art2.strip

  end

  def processPage(url)
    @log.info(url)
    page = @agent.get(url)
    sleep(rand(0.3)+0.3)

    art=proc_art(page.search('span.page-header__article-text')[0].text.gsub('APT. ','').strip,'','')


    @log.info "Using art #{art}"

    pics_urls=page.search('.viewer__img-wrap img').map {|img| img.attr('src').to_s}

    if @pics[art]==nil then @pics[art]=[] end

    i=0
    pics_urls.each do |url|
      if url!=''
          fn="#{art}~#{i}"
        savePic(url,fn,true)
        @pics[art]<<fn
        i+=1
      end
    end

    txt=page.search('p.product__description-text').text.to_s.strip
    props={}
    page.search('.product__specifications li.specifications__item').each do |li|
      k=li.search('span.specifications__name-text').text.to_s.strip
      v=li.search('span.specifications__value').text.to_s.strip
      props[k]=v
    end
    txt=txt.gsub(/Количество в упаковке: [0-9]+/,'').gsub('Описание:','').gsub(/\u00a0/, ' ').gsub(/[\x0d\x0a]/,' ').gsub(/ +/,' ').strip
    txt+=". #{props.map{|k,v| "#{k}: #{v}"}.join(', ')}"
    txt.gsub!(/\.+/,'.')
    txt.gsub!(/\t/,' ')
    txt.gsub!(/ +/,' ')

    @descs[art]=txt


    begin
        c=Cache.find_by_key("Gulliver_#{art}_desc")
        c.data=txt
        c.save
    rescue
      Cache.create(key:"Gulliver_#{art}_desc",data: txt)
    end

    begin
      c=Cache.find_by_key("Gulliver_#{art}_pics")
      c.data=@pics[art]
      c.save
    rescue
      Cache.create(key:"Gulliver_#{art}_pics",data: @pics[art])
    end

    @purchase.reload
    if @purchase.stop
      @purchase.stop=false
      @purchase.save
      raise "Загрузка прекращена по запросу"
    end

  end

  def processPageShop(url,art2=nil)
    @log.info(url)

    tries=4
    begin
      sleep(rand()*0.5+0.7)
      page = @agent.get(url)


      if art2
        art=art2
      else
        art=proc_art(page.search('.b-catalog-detail-panel__article')[0].text.gsub('Арт.','').strip,'','')
      end

      @log.info "Using art #{art}"

      if @descs[art]==nil then @descs[art]=[] end
      if @pics[art]==nil then @pics[art]=[] end

      prod_id=page.search('#detail-card-info .b-catalog-detail-panel__user-panel a[data-add-to-compare=Y]').first.attr('data-product-id').to_s

      ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest', 'Accept' => 'application/json, text/javascript, */*'}

      sleep(rand()*0.3+0.4)

      pic_page=@agent.get("https://gulliver-toys.ru/ajax/detail_product_popup_gallery.php?id=#{prod_id}&eq=0",ajax_headers)

      pic_page.search('.swiper-container .swiper-slide').each_with_index do |div,i|
        img=div.attr('style').to_s.gsub('background-image:url(','').gsub(')','')
        fn="#{art}~#{i}"
        sleep(rand()*0.2+0.1)
        savePic(img,fn,false)
        @pics[art]<<fn
      end

      txt=''
      if page.search('.l-catalog-detail-description .b-accordion__section-content').count>0
        desc_div=page.search('.l-catalog-detail-description .b-accordion__section-content')[2]
        txt=desc_div.text.gsub('Описание:','').gsub(/\u00a0/, ' ').gsub(/[\x0d\x0a]/,' ').gsub(/ +/,' ').strip unless desc_div.nil?
      end

      @retail_price[art]=page.search('span[itemprop=price]').first.attr('content').to_s.to_f

      @descs[art]=txt


      begin
        c=Cache.find_by_key("Gulliver_#{art}_desc")
        c.data=txt
        c.save
      rescue
        Cache.create(key:"Gulliver_#{art}_desc",data: txt)
      end

      begin
        c=Cache.find_by_key("Gulliver_#{art}_pics")
        c.data=@pics[art]
        c.save
      rescue
        Cache.create(key:"Gulliver_#{art}_pics",data: @pics[art])
      end
    rescue Exception => e
      puts "Exception #{e}"
      @log.debug "Exception #{e}"

      sleep((5-tries)*60)
      tries-=1

      puts "Tries left: #{tries}"
      @log.debug "Tries left: #{tries}"

      retry unless tries==0
      raise
    end

    @purchase.reload
    if @purchase.stop
      @purchase.stop=false
      @purchase.save
      raise "Загрузка прекращена по запросу"
    end

  end


  def processCat(url,top_page=true)
    @log.info(url)

    sleep(rand(0.5)+0.5)

    tries=2
    begin
      page = @agent.get(url)
    rescue Mechanize::ResponseCodeError,Net::HTTPServiceUnavailable  => ex
      @log.debug("Exception #{ex}")
      sleep((3-tries)*30)
      tries-=1
      @log.debug "Tries left: #{tries}"
      retry unless tries==0
    end

    page.search(".catalog-block .js__product a.card").each do |a|
      @log.info "Parsing page"

        begin
          processPage(a.attr('href').to_s)
        rescue Mechanize::Error => e
          @log.info "Error processing #{a.attr('href')} "
          @log.info e.to_s
          @log.info e.backtrace
        end


    end

    if top_page
      last_page=page.search('a.page-pagination__link').last
      if last_page
        last_page=last_page.text.to_i
        (2..last_page).each do |p|
          processCat(url+"?page=#{p}",false )
        end
      end
    end

  end

  def processCatShop(url)
    @log.info(url)
    @purchase.message=url

    tries=2
    begin
      page = @agent.get(url)
    rescue Mechanize::ResponseCodeError,Net::HTTPServiceUnavailable  => ex
      @log.debug("Exception #{ex}")
      sleep((3-tries)*30)
      tries-=1
      @log.debug "Tries left: #{tries}"
      retry unless tries==0
    end

    page.search(".b-catalog__content .b-catalog-item").each do |div|
      next if div.search('.b-catalog-item__article').length==0
      art=div.search('.b-catalog-item__article')[0].text.gsub('Артикул: ','').gsub('*','').gsub('/','_').strip
      @log.info "Art from list #{art}"

      prod=@purchase.find_product_by_art(art)
      if prod
        retail_price=div.search('.b-catalog-item__price').text.gsub(/[^0-9.,]/,'')
        prod.rrp=retail_price
        prod.save
        next
      end

      next if @pics[art]!=nil and not @pics[art].empty?

      next if not @arts_in_file.include? art

      @log.info "Parsing page"

        @pics[art]=[]
        a=div.search(".b-catalog-item__title a")[0]

        begin
          processPageShop(a.attr('href').to_s)
        rescue Mechanize::Error => e
          @log.info "Error processing #{a.attr('href').to_s} "
          @log.info e.to_s
          @log.info e.backtrace
        end
    end
  end



  def process_file(f,read_arts=false)
    @purchase.message='Обработка файла'
    book = Spreadsheet.open f

    sheet1 = book.worksheets.find {|s| s.visibility==:visible}
    rails 'No sheets' if sheet1.nil?

    titles={
        'Артикул'=>:art,
        'Номенклатура'=>:name,
        'Новинка'=>:is_new,
        'Действующие акции'=>:is_sale,
        'Спецпредложение'=>:is_sale2,
        'Ссылка'=>:url,
        'Остаток, шт'=>:stock,
        'Базовая цена'=>:price,
        'Штрих-код'=>:barcode
    }

    cols=Hash.new

    title_row=0
    i=0

    in_data=false

    colName=''

    sheet1.each do |row|
      j=0

      if in_data
        if row[cols[:name]]==nil and row[cols[:art]]!=nil
          colName=row[cols[:art]]
          next
        end

        if row[cols[:art]]==nil then next end

        is_new=false
        is_sale=false
        name=row[cols[:name]].strip
        price=row[cols[:price]]

        stock=row[cols[:stock]].to_i

        next if stock==0

        next if price.nil?

        price=price.to_s.gsub(' ','').to_f

        art=row[cols[:art]].to_s.gsub(/\u00a0/, ' ').strip
        art2=proc_art(row[cols[:art]].to_s,colName,name)

        art.gsub!(/\.0$/,'')
        art2.gsub!(/\.0$/,'')

        @log.info art

        if read_arts
          @arts_in_file<<art.gsub('*','').gsub('/','_').strip if not @arts_in_file.include? art.gsub('*','').gsub('/','_').strip
          next
        end

        if price==nil then next end

        if cols.has_key? :is_new then is_new=(row[cols[:is_new]]!=nil and row[cols[:is_new]]!='') end
        if cols.has_key? :is_sale then is_sale=(row[cols[:is_sale]]!=nil and row[cols[:is_sale]]!='') end
        if cols.has_key? :is_sale2 then is_sale|=(row[cols[:is_sale2]]!=nil and row[cols[:is_sale2]]!=nil) end

        #if colName.include? 'для стойки' then next end

        #price*=1.05

        #TODO: price hack
        #if is_sale and price<500 then price*=1.07 end

        #if colName.include? 'МЯГКАЯ' and price<160 then price*=1.2 end

        price=price.ceil

        url=nil
        if cols.has_key?(:url) and cols[:url]!=nil and row[cols[:url]]!=nil and not row[cols[:url]].include? 'old.'
          if row[cols[:url]].instance_of? (Spreadsheet::Link)
            url=row[cols[:url]].chomp ("\x00")
          elsif row[cols[:url]].start_with? 'http'
            url=row[cols[:url]]
          end
        end

        if url.nil? and row[cols[:name]].instance_of?(Spreadsheet::Link)
          url=row[cols[:name]].url.chomp("\x00")
          url=nil if url.include? 'old.'
        end

        desc=Cache.get_value('Gulliver',"#{art2}_desc")
        pics=Cache.get_value('Gulliver',"#{art2}_pics")

        barcode=row[cols[:url]] if cols[:url]

        if desc==nil
          @log.info "Art #{art}, no data"
          unless url.nil?
            @log.info "Art #{art} has url in file #{url}"
            begin
              if url.include? 'toys'
                processPageShop(url, art)
              else
                processPage(url, art)
              end
            rescue Mechanize::Error => e
              @log.info "Error processing #{url}"
              @log.info e.to_s
              @log.info e.backtrace
            end
          end
        end

        col=colName
        if is_new then col=colName+' - НОВИНКИ' end
        if is_sale then col=colName+' - СКИДКИ' end

        #if @descs[art2]==nil
        #          fname="#{@datapath}#{art2}.txt"
        # if File.exist? fname
        #   t=File.binread(fname).bytes[0]
        #   if t==0xD0
        #     @descs[art2]=File.read(fname)
        #   else
        #     @descs[art2]=File.read(fname,:encoding => 'cp1251')
        #   end
        # end

        if pics==nil or pics.empty?
          fname="#{@picpath}#{art2}.jpg"
          if File.exist? fname then pics=["#{art2}"] end
        end

        if pics==nil or pics.empty?
          art2.gsub!('pm','')
          fname="#{@picpath}#{art2}.jpg"
          if File.exist? fname then pics=["#{art2}"] end
        end

        next if pics==nil or pics.empty?

        if name.include? 'стенд' or name.include? 'испенсер' or name.include? 'елфтокер' or name.include? 'тойка' or name.include? 'топпер' or name.include? 'исплей' or name.include? 'ифлит'
          next
        end

        col=addCollection(col,'146')

        price=@prices[art] if @prices.has_key? art


        desc = desc.gsub(/Количество в упаковке: [0-9]+(шт)?/, '') unless desc.nil?


        prod=@purchase.find_product_by_art(art)
        rrp=prod.rrp.to_f if prod

        @log.info "#{art} price: #{price}, retail: #{rrp}"

        #TODO: price hack
        price=(price.to_f*0.9).round if rrp and price.to_f*1.23>rrp
        #price=(price.to_f*0.9).round if @retail_price[art] and price.to_f*1.23>@retail_price[art]
        #price=(@retail_price[art]/1.24).round if @retail_price[art] and price.to_f*1.23>@retail_price[art]

        @log.info "#{art} price2: #{price}, retail: #{rrp}"

        next if @retail_price[art] and @retail_price[art]<price*1.23

        p=addProduct(col,art,name,price,desc,["-@#{stock}"],pics,barcode: barcode,sale: is_sale)
        p.rrp=nil
        p.save

      end


      if not in_data
        row.each do |cell|
          @log.info cell
          if titles.has_key? cell
            cols[titles[cell]]=j
            in_data=true
          end
          j+=1
        end
        if in_data
          @log.info cols
        end
      end
      i+=1
    end
  end

  def process_file_sale(f)
    book = Spreadsheet.open f

    sheet1 = book.worksheet 0

    titles={
        'Артикул'=>:art,
        'Наименование'=>:name,
        'Торговая марка'=>:category,
        'Цена'=>:price
    }

    cols=Hash.new

    title_row=0
    i=0

    in_data=false

    colName=''

    sheet1.each do |row|
      j=0

      if in_data

        if row[cols[:art]]==nil then next end

        name=row[cols[:name]].strip
        price=row[cols[:price]]
        colName=row[cols[:category]]

        art=row[cols[:art]].to_s.gsub(/\u00a0/, ' ').strip
        art2=proc_art(row[cols[:art]].to_s,colName,name)

        art.gsub!(/\.0$/,'')
        art2.gsub!(/\.0$/,'')

        @log.info art

        if price==nil then next end

        price=price.ceil

        if @descs[art2]==nil
          @log.info "Art #{art}, no data"
        end

        col='!'+colName+' - ликвидация'

        if @descs[art2]==nil
          fname="#{@datapath}#{art2}.txt"
          if File.exist? fname
            t=File.binread(fname).bytes[0]
            if t==0xD0
              @descs[art2]=File.read(fname)
            else
              @descs[art2]=File.read(fname,:encoding => 'cp1251')
            end
          end
        end

        if @pics[art2]==nil or @pics[art2].empty?
          fname="#{@picpath}#{art2}.jpg"
          if File.exist? fname then @pics[art2]=["#{art2}"] end
        end

        if @pics[art2]==nil or @pics[art2].empty?
          art2.gsub!('pm','')
          fname="#{@picpath}#{art2}.jpg"
          if File.exist? fname then @pics[art2]=["#{art2}"] end
        end

        if colName.include? "KUSO"
          @descs[art2]='' if @descs[art2].nil?

          @descs[art2]=name+' '+@descs[art2]
          Dir['/home/<USER>/www/spup/kuso/*.jpg'].each do |f|
            f2=f.gsub('K','К')
            if f2.include? art2
              @pics[art2]=[] if @pics[art2].nil?
              @pics[art2]<<f
            end
          end
        end


        if name.include? 'стенд' or name.include? 'испенсер' or name.include? 'елфтокер' or name.include? 'тойка' or name.include? 'топпер' or name.include? 'исплей' or name.include? 'ифлит'
          next
        end

        col=addCollection(col,'146')

        @prices[art]=price

        @descs[art2]=@descs[art2].gsub(/Количество в упаковке: [0-9]+(шт)?/,'') if not @descs[art2].nil?

        addProduct(col,art,name,price,@descs[art2],[],@pics[art2])
      end

      if not in_data
        row.each do |cell|
          @log.info cell
          if titles.has_key? cell
            cols[titles[cell]]=j
            in_data=true
          end
          j+=1
        end
        if in_data
          @log.info cols
        end
      end
      i+=1
    end
  end


  def run
    if @agent==nil then before end

    @prices=Hash.new

    @arts_in_file=[]

    process_file(@price_file,true)
    @log.info @arts_in_file

    #process_previous(1)
    #
    if @parse_sites
      processCat('https://www.gulliver.ru/catalog/igrushki/devochki/')
      processCat('https://www.gulliver.ru/catalog/igrushki/malchiki/')
      processCat('https://www.gulliver.ru/catalog/igrushki/igrushki-dlya-malyshey/')
    end

    process_file_sale(@price_file_sale) if not @price_file_sale.nil?
    process_file(@price_file)

  end

  #handle_asynchronously :run

end