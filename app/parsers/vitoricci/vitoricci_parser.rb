module Vitoricci
  class VitoricciParser < Downloader
    include InvoiceVitoricci

    def initialize
      @dlparams = {}
      @purchase_tags = ['Одежда','Купальники']
      super
    end

    def process_cat(id, cat_name)
      @log.info "Process cat #{id}"

      params = [
        ['act', 'export'],
        ['category_id[]', id],
        ['field[]', 'sku'],
        ['field[]', 'name'],
        ['field[]', 'price'],
        ['field[]', 'desc'],
        ['field[]', 'url_image'],
        ['field[]', 'url_product'],
        ['field[]', 'size'],
        ['field[]', 'ext_images'],
        ['type_data', 'csv'],
        ['ext_price', '0'],
        ['type_format', 'allsize'],
        ['email_export', '']
      ]


      page = @agent.post('https://sp.vitoricci.ru/information.php?pages_id=34', params)

      conv=Encoding::Converter.new('Windows-1251','UTF-8',:invalid => :replace, :undef => :replace)
      body=conv.convert(page.body)

      CSV.parse(body,:col_sep=>';', :headers => true) do |l|
        art=l['Артикул']
        pic_urls=l[7..-1]
        pics=[]
        pic_urls.each_with_index do |url,i|
          pics<<savePic(url,"#{art}~#{i}",false)
        end

        col=addCollection(cat_name,'0')
        addProduct(col,art,l['Наименование'],l['Цена'],l['Описание'],l['Размер'].split(','),pics,nil,0,nil,source: l['Ссылка на товар'],brand_name: 'Vitoricci')
      end
    rescue Net::HTTPInternalServerError => exception
      @log.debug "Error 500"
    rescue Mechanize::ResponseCodeError => exception
      @log.debug "Error #{exception.response_code}"
    end

    def run
      page = @agent.get 'https://sp.vitoricci.ru/information.php?pages_id=34'

      page.search("input[name='category_id[]']").each { |inp|
        cat_name = inp.parent.search('label').text
        cat_id = inp.attr('value').to_s
        process_cat(cat_id, cat_name)
      }
    end
  end
end
