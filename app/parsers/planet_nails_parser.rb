require_relative '../parsers/framework/parsed_product'
#require_relative '../parsers/framework/extensions'
#require 'downloader'

require 'mechanize'
require 'spreadsheet'

class PlanetNailsParser < Downloader
  attr_accessor :price_file
  include InvoiceSimple

  def initialize ()
    @dlparams={'price_file'=>:file}
    @site_url = 'http://www.planet-nails.ru/'
    @products = Array.new
    @excel_products = []
    @error_length = 0
    super
  end

  def run
    before if @agent == nil

    @log.debug 'START PARSE EXCEL'
    parse_excel
    @log.debug 'END PARSE EXCEL'

    @log.debug 'START PARSE PLANET NAILS'
    @log.debug 'START PARSE SITE'
    parse_site
    @log.debug 'END PARSE SITE'

    @log.debug 'START VERIFY DATA'
    verify_data
    @log.debug 'END VERIFY DATA'

    #add_to_database
    @log.debug 'END PARSE PLANET NAILS'
  end

  def add_to_database
    error_articuls = []

    @excel_products.each do |product|
      next if product.price == 0
      product_index = @products.index {|p| p.articul == product.articul}
      if product_index.nil?
        error_articuls.push(product.articul)
        next
      end
      site_product = @products[product_index]
      saved_images=[]
      site_product.images.each_with_index { |url,i |
        next if url.include? 'zaglushka'
        savePic('http://www.planet-nails.ru/'+url,"#{site_product.articul.gsub('/', '_')}~#{i}",false)
        saved_images<<"#{site_product.articul.gsub('/', '_')}~#{i}"
      }
      category = site_product.category.nil? ? 'Без категории' : site_product.category
      col=addCollection(category,'4685')
      addProduct(col, site_product.articul,site_product.name, product.price, site_product.description, [], saved_images)
      addProductNew(nil,col.name,nil,site_product.articul,site_product.name,site_product.description,[],product.price)
    end
    @log.debug "Products in price and not in site length: #{error_articuls.length}"
    error_articuls.each {|art| @log.debug art}
  end

  def addCollection(name,type)
    name_lower = prepare_string(name).downcase.to_s

    (1..100).each do |i|
      key = "#{name_lower}"
      key = "#{name_lower} - #{i}" if i>1

      exist_collection_key_index = @collections.keys.index{|k|k.include? key}

      unless exist_collection_key_index.nil?
        exist_key = @collections.keys[exist_collection_key_index]
        @collections[exist_key].reload
        @log.debug @collections[exist_key].products.length
        return @collections[exist_key] if @collections[exist_key].products.length<200
        next
      end

      col=Collection.new
      col.name= i>1 ? "#{prepare_string(name)} - #{i}" : "#{prepare_string(name)}"
      col.coltype=type
      col.purchase=@purchase
      col.save

      @collections[key]=col
      return @collections[key]
    end
  end

  def parse_excel
    book = Spreadsheet.open @price_file

    ws = book.worksheet 0


    category = ''
    ws.each do |row|
      checkStop
      next if row.nil? or row[2].nil?

      category = prepare_string row[2].to_s if row[1].nil?

      next if row[1].nil?
      @log.debug category
      articul = prepare_string row[1].to_s

      next if articul.match(/\d+/).nil?

      name = prepare_string row[2].to_s

      price = (row[3].to_s.strip.gsub(' ', '').to_f/1.3).floor

      opt_price=row[6].to_s.strip.gsub(' ', '').to_f.ceil
      price=opt_price if price<opt_price

      @log.debug "#{category} #{articul} #{name} #{price}"

      @excel_products.push Framework::ParsedProduct.new(category, articul, [], name, '', price)
    end

    @log.debug "Excel_Product length: #{@excel_products.length}"
  end

  def concatenate_products
    additional_products = []

    @excel_products.each{|prod|
      product_index = @products.index {|p| p.articul == prod.articul}

      if product_index.nil?
        @log.debug "ERROR: Not found product with articul #{prod.articul}"
        additional_products.push prod
        next
      end

      product = @products[product_index]
      product.price = prod.price
    }
    @products.concat additional_products
  end

  def parse_site
    parse_catalog @site_url
  end

  def parse_catalog(site_url)
    # Category_links $$('.menu_inner ul.dropdown > li  > a')
    category_links = @agent.get(site_url).search('li.ty-menu__submenu-item a.ty-menu__submenu-link').map{|a|a.attr('href').to_s+'?items_per_page=1200'}

    category_links.each do |link|
      @log.debug "Category #{link}"

      category_page = @agent.get(link)

      product_links = category_page.search('.main-content-grid a.product-title').map{|a|a.attr('href').to_s}

      product_links.each{|l| process_product(l) }
    end

    # Page_links http://www.planet-nails.ru/GEL-DLYA-NARASHCHIVANIYA-NOGTEY-page-9.html
    # http://www.planet-nails.ru/LAK-DLYA-NOGTEY-page-17.html Переходить два раза надо
    # Так что может ,просто тыкать на "Следующая" до посинения $$('.next'). Пока в ней не будет ссылки или пока ее самой не будет

    @log.debug "Products length: #{@products.length}"
  end

  def process_product(product_url)
    checkStop
    product_page = @agent.get(product_url)

    breadcrumbs = product_page.search('.ty-breadcrumbs a.ty-breadcrumbs__a')
    category = "#{breadcrumbs[-2].text} - #{breadcrumbs[-1].text}"

    articul = prepare_string product_page.search('meta[itemprop=sku]').attr('content').to_s
    name = prepare_string product_page.search('meta[itemprop=name]').attr('content').to_s
    description = prepare_string product_page.search('meta[itemprop=description]').attr('content').to_s


    gallery_images = product_page.search('.ty-product-img a')

    images = gallery_images.map{|a|a.attr('href').to_s}

    props={}
    size=''
    product_page.search('#content_features .ty-product-feature').each {|el|
      k=el.search('span')[0].text.gsub(':','').strip.downcase
      v=el.search('div')[0].text.strip.downcase

      next if k=='вес для почты' or k=='торговая марка'
      k='страна' if k=='страна производитель'

      if k=='вес' or k=='объем'
        size=v
        next
      end

      props[k]=v
    }
    features_description = props.map {|k,v| "#{k}: #{v}"}.join ', '
    features_description = features_description.gsub('.,', '.') unless features_description.nil?
    features_description = features_description.gsub(':,', ':') unless features_description.nil?

    description = "#{description}. #{features_description.humanize}"
    description.gsub!('&nbsp;',' ')
    description.gsub!(/ +/,' ')
    description.gsub!(/\.+/,'.')

    ActionView::Base.full_sanitizer.sanitize(description)

    price=0

    site_product = Framework::ParsedProduct.new(category, articul, images, name, description, price)
    excel_product_index = @excel_products.index {|p| p.articul == articul}
    return if excel_product_index.nil?

    saved_images=[]
    site_product.images.each_with_index { |url,i |
      next if url.include? 'zaglushka'
      next if url.strip==''
      url = 'https://www.planet-nails.ru/'+url unless url.include? 'https://'

      name = "#{site_product.articul.gsub('/', '_')}~#{i}"
      savePic(url, name, true)
      path = "#{@picpath}#{name}.jpg"
      if File.exist? path
        hash = Digest::MD5.hexdigest(File.read(path))
        fsize = File.size(path)
        saved_images << {'path' => name, 'hash' => hash, 'size' => fsize}
      else
        saved_images << {'path' => name, 'hash' => '', 'size' => 0}
      end
    }
    json = {''=>{'sizes'=>{''=>{'price'=>@excel_products[excel_product_index].price, 'weight'=>0.2 }}, 'pics'=>saved_images}}
    category = site_product.category.nil? ? 'Без категории' : site_product.category
    col=addCollection(category,'4685')

    if description.length>500
      truncate=500-features_description.humanize.length-5
      description=description[0..truncate]+'... '
      description = "#{description}. #{features_description.humanize}"
    end

    addProduct(col, site_product.articul,site_product.name, @excel_products[excel_product_index].price, description, [size], saved_images.map{|img|img['path']})
  end

  def verify_data
    # Просто бежим по коллекции и показываем тот товар, у которого чего-то нет

    image_regex = /(.+)\.(?:jpe?g|gif|png|JPE?G|GIF|PNG)/

    @products.each do |product|
      #category, articul, images, name, description, price, sizes

      log_if (product.category.nil?), product, 'ERROR: Category is empty'
      log_if product.articul.nil?, product, 'ERROR: Articul is empty'
      log_if (product.images.nil? or not product.images.length), product, 'ERROR: Images is empty'
      log_if (product.images.any? {|i| i.match(image_regex).nil?}), product, 'ERROR: One of image URIs has wrong format'
      log_if product.name.nil?, product, 'ERROR: Name is empty'
      log_if product.price.nil?, product, 'ERROR: Price is empty'
      log_if product.price == 0, product, 'ERROR: Price is equal 0'
      log_if (product.price.is_a? String), product, 'ERROR: Price is not a number'
      log_if ( product.category.include?("Кольца") and (product.sizes.nil? or not product.sizes.length)), product, 'ERROR: Sizes is empty'
    end

    @log.debug "Error length:#{@error_length}"
  end

  def log_if(condition, product, msg='')
    if condition
      if !msg.nil?
        @log.debug msg
      end
      log_product product
      @error_length += 1
    end
  end

  def log_product(product)
    @log.debug 'LOG PRODUCT'
    @log.debug "Category:#{product.category}"
    @log.debug "Name:#{product.name}"
    @log.debug "Articul:#{product.articul}"
    @log.debug "Price:#{product.price}"
    if product.images.nil? or not product.images.length
      @log.debug 'Images length 0'
    else
      @log.debug "Images length:#{product.images.length}"
      product.images.each {|i| @log.debug i}
    end
    @log.debug "Description:#{product.description}"
    @log.debug 'END LOG PRODUCT'
  end
end
