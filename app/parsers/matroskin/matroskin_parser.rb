#encoding:utf-8
require_relative '../framework/parser'
require_relative 'matroskin_product_config'
require 'htmlentities'

module <PERSON><PERSON>kin
  class MatroskinParser < Framework::<PERSON><PERSON><PERSON>
    def initialize
      @json_products = {}
      super
    end

    def initialize_settings
      with_url('http://matroskin.spb.ru/index.php?route=product/category&path=60')
          .with_category_links('.li-no-active-2 > a')
          .with_pagination('.pagination > .links>a:last-child')
          .with_category(->(category_page) {
            category_page.search('.breadcrumb > a').drop(1).map { |a| prepare_string a.text }.join ', '
          })
          .with_product_config(->(pc) {
            pc.with_product_selector('.tovar > .image > a')
                .with_articul(->(product_page) {
                  product_page.search('.cart input').first.attr('value').to_s
                })
                .with_name(->(product_page) {
                  prepare_string product_page.search('h1').first.text
                })
                .with_sizes(->(product_page) {
                  div = product_page.search('.option').find { |div| div.text.downcase.to_s.include? 'размер' }

                  div.search('option').drop(1).map { |opt| prepare_string opt.text }
                })
                .with_price(->(product_page) {
                  result = product_page.search('div.price').first.text.to_s.gsub(' ', '')
                  result = result.match(/([\d\.]+)/).captures[0]

                  result = result.to_f.round(2)
                  (result*0.8).ceil
                })
                .with_description(->(product_page) {
                  result = product_page.search('.long-info p').first.text.gsub('<br>', ' ')

                  prepare_string HTMLEntities.new.decode(result)
                })
                .with_images(->(product_page) {
                  result = product_page.search('.left > .image > a').map { |img| img.attr('href').to_s }
                  result.concat(product_page.search('.image-additional > a').map { |img| img.attr('href').to_s })
                  result.uniq
                })
                .with_colors(->(product_page) {
                  color_match = product_page.search('h1').first.text.match(/расцветка\s+([а-яА-ЯёЁa-zA-Z]+)( +[а-яА-ЯёЁa-zA-Z]+)?/)
                  if color_match.nil?
                    div = product_page.search('.option').find { |div| div.text.downcase.to_s.include? 'расцветка' }
                    div.search('option').drop(1).map { |opt| prepare_string opt.text }
                  else
                    [prepare_string(color_match.captures.map { |c| c.to_s }.join ' ')]
                  end
                })
                .with_image_config(->(ic) {
                  ic.with_default_url('http://www.matroskin.spb.ru/')
                })
                .with_category_type('2426')
                .with_category_type('960', 'ветровки')
                .with_category_type('561', 'брюки')
                .with_category_type('562', 'жилетки')
                .with_category_type('563', 'костюмы')
                .with_category_type('561', 'полукомбинезоны')
                .with_category_type('563', 'комбинезоны')
                .with_category_type('960', 'куртки')
          }, Matroskin::MatroskinProductConfig)
    end
  end
end