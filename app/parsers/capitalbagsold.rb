#encoding: utf-8

require 'downloader'
require 'csv'

class  Capitalbagsold < Downloader
  attr_accessor :login
  attr_accessor :password
  attr_accessor :data

  def initialize
    @price_file=''
    @dlparams={'login'=>:text,'password'=>:password,'data'=>:textarea}

    super
  end


  def processCat(url,suff)
    tries ||= 2

    @log.info(url)
    puts url
    page = @agent.get(url)

    page.search("table tr")[1..-1].each do |tr|
      if tr.search('td').size!=6 then next end

      pic=tr.search('a')[0].attr('href')
      name=tr.search('td')[1].text.strip
      pid=tr.search('input')[0].attr('name')
      price=tr.search('td')[4].text.strip

      price=((price.to_f*1.2).ceil).to_s #if not suff.nil? and suff.downcase.include? 'sale'
      pic=url.split('?')[0]+pic

      puts pic
      puts name
      puts pid
      puts price

      desc=name

      #if not File.exist? "#{@datapath}#{pid}.txt" then getInfo(pid) end

      #if File.exist? "#{@datapath}#{pid}.txt" then desc+=File.read("#{@datapath}#{pid}.txt") end


      colName = name.gsub(/ .*/, '')

      catType='114'
      if name.start_with? "Кошелек женский"
        catType='1179'
        colName = "Кошелек женский";
      elsif name.start_with? "Кошелек мужской"
        catType='875'
        colName = "Кошелек мужской"
      end

      if not suff.nil? then colName+=suff end
      puts colName

      pics=[pid]
      desc=$descs[pid] if not $descs[pid].nil?

      if not $pics[pid].nil?
        pics=$pics[pid]
      else
        savePic(pic,pid,false)
      end


      col=addCollection(colName,catType)
      addProduct(col,pid,name,price,desc,[],pics)

    end

  end

  def processNewCat(url)
    tries ||= 2

    @log.info(url)
    puts url
    page = @agent.get(url)
    body=page.body.gsub("<div style='position: relative;>","<div style='position: relative;'>")
    page = Nokogiri::HTML(body)

    page.search('.goodsgroup').each do |div1|

      sizePic=div1.search('.goods img')[0]

      next if sizePic.nil?

      size=''
      if sizePic.attr('src')=='/pic/size_1.png'
        divs=sizePic.parent.search('div')
        size="Высота: #{divs[0].text} см, длина: #{divs[1].text} см, глубина: #{divs[2].text} см, длина ручки: #{divs[3].text} см"
      elsif sizePic.attr('src')=='/pic/size_2.png'
        divs=sizePic.parent.search('div')
        size="Высота: #{divs[0].text} см, длина: #{divs[1].text} см"
      end
      puts size
      div1.search('.highslide-caption').each do |div2|

        name=div2.search('b')[0].text.strip
        price=div2.search('table')[0].search('td')[2].text.strip

        art=div2.search('input')[1].attr('id').gsub('cnt','')

        pics=div2.search('center span img').map{|img| img.attr('src').gsub('thumb/','/')}

        puts art
        puts name
        puts price

        pics2=[]
        pics.each_with_index do |p,i|
          savePic(p,"#{art}~#{i}",false)
          pics2<<"#{art}~#{i}"
        end
        puts pics

        $descs[art]=size
        $pics[art]=pics2
      end

    end



  end


  def run

    $descs=Hash.new
    $pics=Hash.new

    page = @agent.get('http://www.capital-bags.ru/home')

    login_form=page.forms[0]
    login_form.user=@login
    login_form.pass=@password
    page = @agent.submit(login_form, login_form.buttons.first)

    page.search("#cb_center a").each do |a|
      next if not a.attr('href').include? '/magaz/'

      processNewCat(a.attr('href')+"/showall")
    end


    CSV.parse(@data,:col_sep=>';', :headers => false,:encoding=>'UTF-8') do |fields|
      href=fields[0]
      suff=fields[1]

      processCat(href,suff)
    end

  end
  #handle_asynchronously :run

end