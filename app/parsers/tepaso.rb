#encoding: utf-8

require 'downloader'
#require 'logger'
#require 'unicode_utils/downcase'

class Tepaso < Downloader

  def initialize
    @dlparams={}
    @piccount=0

    super
  end


  def processPage(url)
    @log.info(url)
    page = @agent.get(url)
    puts url

    art1=''
    if %r!goods/(.*)!=~url
      art1=$1
      art1.gsub!(/\?.*/,'')
    end

    #conv=Encoding::Converter.new('Windows-1251','UTF-8')
    #body=conv.convert(page.body)
    #body=page.body.gsub(%r!</option>\s+</div>!,'</option>')
    #page = Nokogiri::HTML(body)

    return if page.search('strong[itemprop=productID]').length==0

    art=page.search('strong[itemprop=productID] span')[0].text.strip.gsub('/','_')

    cat=page.search('.breadcrumbs_box a')[2].text.strip
    cat=page.search('.breadcrumbs_box a')[2..3].map {|a| a.text.strip}.join '-' if page.search('.breadcrumbs_box a').length>3

    name=page.search('h1[itemprop=name]')[0].text.strip

    desc=''
    desc=page.search('div[itemprop=description]')[0].text.strip.gsub(/[\r\n\t ]+/,' ') if page.search('div[itemprop=description]').length>0

    price=page.search('span[itemprop=price]')[0].attr('content')

    sizes=Hash.new
    page.search('select')[0].search('option').each do |o|
      puts o.text.strip
      sizes[o.attr('value')]=o.text.strip
    end

    colors=Hash.new
    if page.search('select').length>1
        page.search('select')[1].search('option').each do |o|
          puts o.attr('disabled')
          puts o.text.strip
          colors[o.attr('value')]=o.text.strip
        end
    else
      colors['0']='как на картинке'
    end

    qties=Hash.new

    stock=Hash.new
    page.search('.goodsDataMainModificationsList').each do |div|
      puts div.attr('rel')
      ids=div.attr('rel').split('_')
      qty=div.search('input[name=rest_value]')[0].attr('value')

      ids<<'0' if ids.length==1
      if sizes.include? ids[1]
          t=ids[1]
          ids[1]=ids[0]
          ids[0]=t
      end


       puts ids
      next if qty=='0'
      qties[ids[1]+'_'+ids[0]]=qty

      stock[ids[1]]=[] if not stock.include? ids[1]
      stock[ids[1]]<<ids[0]
    end

    puts stock

    pics=[]
    page.search('#product_image_big_photos a').each_with_index do |a,i|
      fname="#{art1}~#{i}"
      pics<<savePic(a.attr('href'),fname,true)
    end

    desc=desc.strip().gsub(';',',').gsub("\n"," ")

    puts '----'
    puts stock
    puts '----'

    stock.each_key do |colorid|
      sizes2=[]
      q=[]
      stock[colorid].each do |s|
        sizes2<<sizes[s]
        q<<qties[colorid+'_'+s]
      end

      cat_code='367'
      cat_code='53' if cat.include? 'енская'
      cat_code='4684' if cat.include? 'етская'

      col=addCollection(cat,cat_code)
      addProduct(col,art1+' '+art,name,price,"Цвет #{colors[colorid]}. "+desc,sizes2,pics)

    end

  end

  def processCat(url)
    @log.info(url)
    page = @agent.get(url)

    #supercat=false
    #page.search("#content .tabproduct a.orangebar").each do |a|
    #  processCat(a.attr('href')+'?goods_search_field_id=2&per_page=100&goods_view_type=2')
    #  supercat=true
    #end

    #return if supercat

    #page.search("#content .goodstable").each do |div|
    #  processPage(div.search('.title a')[0].attr('href'))
    #end


    page.search(".product a.readmore").each do |a|
        processPage(a.attr('href'))
    end

  end

  def run
    page = @agent.get('http://tepaso.ru/catalog')

    page.search('#columnLeft .infoBoxCategory ul li:not(.parent) a').each do |a|
        processCat(a.attr('href')+'?goods_search_field_id=2&per_page=100')
    end

  end
  #handle_asynchronously :run

end