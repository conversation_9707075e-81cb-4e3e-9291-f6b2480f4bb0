#encoding:utf-8
require_relative '../framework/site/product_config'

module ToysOstCom
  class ToysOstComProductConfig < Framework::Site::ProductConfig

    def get_page(link)
      begin
        @log_func.call link
        sleep(rand(0.8) + 0.9)
        result = super(link)
        @log_func.call(result.body.to_s)
        result
      rescue Mechanize::ResponseCodeError => exception
        @error_log_func.call "Has exception on #{link}, response code - #{exception.response_code}"
        if exception.response_code == '404'
          return nil
        else
          @error_log_func.call "Has exception on #{link}, restart agent"
          @agent.shutdown
          sleep(10.minutes)
          @agent = @create_agent_func.call
          @agent.request_headers
          return @agent.get("http://ost-com.ru/#{link}")
        end
      rescue
        @error_log_func.call "Has exception on #{link}, restart agent"
        @agent.shutdown
        @agent = @create_agent_func.call
        @agent.request_headers
        sleep(10.minutes)
        return @agent.get("http://ost-com.ru/#{link}")
      end
    end


    def create_new_product(product_page, link=nil)
      puts 'create new product'
      product = parse_product(product_page, link)

      if product.nil?
        @error_log_func.call "Product is nil for link #{link} and element #{product_page}"
        return
      end
      @purchase.check_stop

      @log_func.call(product.articul)
      puts product.articul
      excel_products = @get_excel_products.call.find_all{|p|p.articul == product.articul}

      return if excel_products.empty?
      excel_products.each do |excel_product|
        if excel_product.category.blank?
          excel_product.category = product.category
        else
          excel_product.category = "#{product.category} - Скидки"
        end
        excel_product.images = product.images.map{|i|i}
        excel_product.name = product.name
        excel_product.description = product.description
        add_product(excel_product)
      end
    end

    def with_excel_products(get_excel_products)
      @get_excel_products = get_excel_products
      self
    end

    def with_create_new_agent_func(create_agent_func)
      @create_agent_func = create_agent_func
      self
    end
  end
end