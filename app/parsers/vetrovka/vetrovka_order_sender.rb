#encoding:utf-8

module Vetrovka
  class VetrovkaOrderSender < Framework::OrderSender

    def initialize_settings
      @agent.verify_mode = OpenSSL::SSL::VERIFY_NONE
    end

    def add_csv_data(fields)
      art=fields['Артикул'].to_s
      size = fields['Размер'].to_s.gsub(/ \(\d+ шт.\)/,'')
      @csv_data[art]={} unless @csv_data.include? art
      if @csv_data[art].include? size
        @csv_data[art][size] +=1
      else
        @csv_data[art][size] = 1
      end
    end

    def authorize
      @agent.post('https://vetrovka.ru/?login=yes', {
          "USER_LOGIN" => 'shopkaskad',
          "USER_PASSWORD" => '270279',
          "USER_REMEMBER" => "Y",
          "backurl" => "/",
          "AUTH_FORM" => "Y",
          "TYPE" => "AUTH"
      })
    end

    def clean
      basket_page = @agent.get('https://vetrovka.ru/personal/cart/')
      urls = basket_page.search('.control > a').map{|a|a.attr('href').to_s}
      urls.each{|url|@agent.get url}
    end

    def order(art, sizes)
      debug_func "order #{art} #{sizes}"

      page = @agent.get("https://www.vetrovka.ru/search/?q="+art)
      product_form = page.search('.bx_catalog_item')
      if product_form.length > 1
        add_error_csv_data("Have #{product_form.length} products with articul", art)
        product_form = product_form.find{|form|
          match = form.search('.bx_catalog_item_articul_visible').text.match(/\d+/)
          not match.nil? and match.to_s == art
        }
      else
        product_form = product_form.first
      end
      if product_form.nil?
        add_error_csv_data("Not found products", art)
        return
      end

      product_url = product_form.search('.ice_product_img > a').first.attr('href').to_s
      product_page = nil
      begin
        product_page = @agent.get(product_url)
        unless product_page.search('.bx-404-container').first.nil?
          add_error_csv_data("Cannot open page with articul: #{art} and href #{product_url}", art)
          return
        end
      rescue Mechanize::ResponseCodeError => exception
        add_error_csv_data("Cannot open page with articul: #{art} and href #{product_url}", art)
        error_func exception.to_s
        return
      end

      return if product_page.nil?

      result_json = {}
      sizes.each do |size, quantity|
        if size == '-'
          row = product_page.search('.row_article').first
        else
          row = product_page.search('.row_article').find { |row| row.search('.cell_name').first.text.gsub(/ \(\d+ шт.\)/,'').strip == size }
        end
        if row.nil?
          add_error_csv_data("Cannot find size", art, nil, size, quantity)
          next
        end
        key = row.search('.quantity-input').attr('name').to_s
        result_json[key] = quantity if quantity > 0
      end
      result_json['js_action'] = 'add2basket'
      @agent.post('https://vetrovka.ru/catalog/ajax.php', result_json)
    end
  end
end