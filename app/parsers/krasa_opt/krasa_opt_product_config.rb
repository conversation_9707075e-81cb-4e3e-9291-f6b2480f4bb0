#encoding:utf-8
require_relative '../framework/site/product_config'

module KrasaOpt
  class KrasaOptProductConfig < Framework::Site::ProductConfig
    def parse_product(product_page, link=nil)
      category = product_page.search('.breadcrumb > li > a').drop(1).map{|a|prepare_string a.text}.join ', '
      return nil if category.blank?
      result = super(product_page, link)
      return nil if result.articul.blank?
      result
    end
  end
end