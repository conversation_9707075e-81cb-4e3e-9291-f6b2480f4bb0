#encoding: utf-8

require 'downloader'
#require 'logger'
#require 'unicode_utils/downcase'
require 'json'

class Ascomfort < Downloader

  def initialize
    @dlparams={}
    @piccount=0
    super
  end


  def processPage(url)
    @log.info(url)
    puts url
    page = @agent.get(url)
    page = Nokogiri::HTML(page.body.gsub('<br />',"\n").gsub('</p>','</p>. '))

    t=url.split('/')
    art=t[-1]

    desc=page.search('#tab-description p').text.strip

    desc+=page.search('div[itemprop=description]').text.strip
    desc=desc.gsub(/СКИДКИ.*/,'').strip
    name=page.search('h1[itemprop=name]')[0].text.strip

    pics=page.search('a.zoom').map{|a|  a.attr('href')}
    pics2=[]
    pics.each_with_index do |p,i|
      savePic(p,"#{art}~#{i}",false)
      pics2<<"#{art}~#{i}"
    end

    attr_names=page.search('table.shop_attributes th').map {|th| th.text}
    attr_vals=page.search('table.shop_attributes td').map {|td| td.text}
    attr_names.each_with_index do |n,i|
      desc+=' '+n+': '+attr_vals[i]+'. '
    end
    cat=page.search('.breadcrumbs a')[2].text.strip

    col=addCollection(cat,'1003')

    if page.search('p[itemprop=price] span.from').length==0
      sizes=[]
      if page.search('table.variations select').length>0
        page.search('table.variations select')[0].search('option').each do |o|
          next if o.attr('value')==''
          sizes<<o.text.strip
        end
      end

      price=page.search('p[itemprop=price]')[0].text.gsub(/[^0-9]/,'')
      addProduct(col,art,name,price,desc,sizes,pics2)
    elsif page.search('table.variations select').length>0
      var_data=JSON.parse(page.search('form.variations_form').attr('data-product_variations').to_s)
      var1_id=page.search('table.variations select')[0].attr('id')
      page.search('table.variations select')[0].search('option').each do |o|
        if o.attr('value')!=''
          price_html=''
          var_data.each do |d|
            if d['attributes']["attribute_#{var1_id}"]==o.attr('value')
              price=d['price_html'].gsub('<span class="price"><span class="amount">','').gsub(' руб.</span></span>','').gsub(' ','').gsub(/[^0-9]/,'')
              puts o.text,price
              addProduct(col,art,name,price,desc,['Размер '+o.text],pics2)
              pics2=[pics2[0]]
              break
            end
          end
        end
      end
    end
  rescue Mechanize::Error
    puts "Error downloading #{url}"
  rescue SystemCallError
    puts "Error downloading #{url}"

  end


  def processCat(url)
    puts url

    tries ||= 2

    page = @agent.get url
    page.search(".listing_products li.product-category a").each do |a|
        processCat(a.attr('href'))
    end

    page.search(".listing_products li.product_item p a").each do |a|
      processPage(a.attr('href'))
    end

  rescue Mechanize::Error => e
    puts e
    retry unless (tries-=1).zero?

  end

  def run
    if @agent==nil then before end
    page = @agent.get('http://as-comfort.ru/shop/')

    page.search("ul.product-categories li.cat-item a").each do |a|
          processCat(a.attr('href'))
    end

  end
  #handle_asynchronously :run

end