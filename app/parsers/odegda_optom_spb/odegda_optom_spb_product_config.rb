#encoding:utf-8
require_relative '../framework/site/product_config'

module OdegdaOptomSpb
  class OdegdaOptomSpbProductConfig < Framework::Site::ProductConfig
    def initialize
      super
      with_sizes_table(->(product){
        return [] unless product.is_a? Framework::SizeableParsedProduct
        return get_sizes_table_for_one_size if product.sizes.length == 1 and product.sizes[0].include? 'size'
        return get_sizes_table_for_mix_size if product.sizes.any?{|s| s.downcase.to_s.include? 's/m' or s.downcase.to_s.include? 'm/l'}
        get_sizes_table_for_normal_sizes
      })
    end

    def get_sizes_table_for_one_size
      [
          ['Размер One size', 'Единый размер подходит на размеры XS S M (Джемпера и пуловера подходят на размер L)']
      ]
    end

    def get_sizes_table_for_mix_size
      [
          ['Размер S/M-это один универсальный размер,подходящий одновременно размерам S и M.'],
          ['Размер M/L-это один универсальный размер,подходящий одновременно размерам M и L.']
      ]
    end

    def get_sizes_table_for_normal_sizes
      [
          ['Российский размер', '40', '42', '44', '46', '48', '50', '52-54', '54-56'],
          ['Буквенный размер', 'XS', 'S', 'M', 'L', 'XL', 'XXL', '3XL', '4XL'],
          ['Обхват груди', '79-83', '84-88', '89-92', '93-96', '97-100', '101-104', '104-107', '107-110'],
          ['Обхват бедер', '83-87', '88-93', '94-98', '99-104', '105-109', '110-117', '115-122', '127-134']
      ]
    end

    def parse_product(product_page, link=nil)
      result = super(product_page, link)

      result.category = "#{result.category} - Новинки" if @get_new_product_links_func.call.include?(link)

      result
    end

    def create_new_product(product_page, link=nil)
      product = parse_product(product_page, link)
      json = {}
      colors = get_colors(product_page)
      additional = {}
      colors.each do |color_id, color|
        next if color[:articul].blank?
        @get_images = ->(product_page) {
          get_image_uris(product_page, color[:combination_ids])
        }
        clone_product = product.clone
        clone_product.articul = color[:articul]
        clone_product.sizes = color[:sizes]
        product.sizes_table = @get_sizes_table.call(clone_product)
        clone_product.images = save_images(product_page, "#{clone_product.articul}~#{color_id}")
        clone_product.name = "#{clone_product.name} Цвет: #{color[:name]}"
        clone_product.description = prepare_string "Цвет: #{color[:name]}. #{clone_product.description}"
        add_product(clone_product)
        get_json(clone_product, color[:name]).each { |k, v| json[k]=v }
        additional[color[:name]] = {articul: color[:articul]}
      end
      return if product.articul.blank? and additional.length == 0
      product.articul = additional.first.last[:articul] if product.articul.blank?
      product.additional = additional
      add_product_new(product, json)
    end

    def get_colors(product_page)
      # здесь всякая замута с цветами
      # attributesCombinations - colors and sizes flatten
      attributes = get_page_json(product_page, 'attributesCombinations')

      color_ids = attributes.find_all { |a| a['group'] == 'colour' }.map{|a|a['id_attribute']}
      combinations = get_page_json(product_page, 'combinations')
      colors = Hash.new { |hash, key| hash[key] = {:combination_ids => [], :sizes => []} }

      combinations.each do |combination_id, combination_json|
        color_id = combination_json['attributes'][1]
        colors[color_id][:combination_ids] << combination_id
        colors[color_id][:sizes] << combination_json['attributes_values']['1']
        colors[color_id][:name] = combination_json['attributes_values']['2']
        colors[color_id][:articul] = combination_json['reference'] unless combination_json['reference'].blank?
      end

      colors

    end

    def get_image_uris(product_page, combination_ids)
      puts combination_ids
      # combinationImages - images for color. have image_id
      images_json = get_page_json(product_page, 'combinationImages')
      puts images_json
      last_image_part = product_page.search('[itemprop=image]').first.attr('src').split('/')[-1]

      # http://odegdaoptom.spb.ru/4309-large_default/kombinezon-s-kruzhevom.jpg
      # Последнюю часть можно взять из какого-нибудь готового изображения

      image_ids = combination_ids.map{|combination_id| images_json[combination_id].nil? ? [] : images_json[combination_id].map{|i| i['id_image']}}.flatten.uniq

      image_ids.map{|id| "#{id}-large_default/#{last_image_part}"}
    end

    def get_page_json(product_page, variable_name)
      sources = product_page.body.to_s.match(/var #{variable_name} = (?<json>.+);/)['json']
      JSON.parse sources
    end

    def with_new_product_links(get_links_func)
      @get_new_product_links_func = get_links_func
      self
    end
  end
end