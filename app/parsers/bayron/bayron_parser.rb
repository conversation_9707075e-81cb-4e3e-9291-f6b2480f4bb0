#encoding:utf-8

require_relative '../framework/parser'
require_relative 'bayron_product_config'
module Bayron
  class BayronParser < Framework::<PERSON><PERSON><PERSON>
    def initialize_settings
      with_auth_data(->(ad) {
        ad.with_auth_url('http://xn--80abzqel.xn--p1ai/')
            .with_login('<EMAIL>')
            .with_password('240680')
            .with_login_field_name('USER_LOGIN')
            .with_password_field_name('USER_PASSWORD')
            .with_form(->(page) {
              page.forms[1]
            })
      })

      with_url('http://xn--80abzqel.xn--p1ai/')
        .with_category_links('.top_menu > li > a')
        .with_category_links('/catalog/zhenskaya-odez/', true)
        .with_pagination('.paginate > a:last-child')
        .with_product_config(->(pc){
          pc.with_product_selector('.product_item > .name > a')
              .with_category(->(product_page) {
                categories = product_page.search('.breadcrumbs > a').map { |a| prepare_string a.text }.drop(2)
                return categories[0] if categories.length == 1
                "#{categories[0]}, #{categories[1]}"
              })
              .with_name(->(product_page) {
                product_page.search('h1').first.text
              })
              .with_articul(->(product_page) {
                art = product_page.search('.art').first
                return art.text unless art.nil?
                art = product_page.search('h1').first.text
                return "# #{art}"
              }, /# (?<articul>.*)/)
              .with_description(->(product_page) {
                prepare_string product_page.search('.description > p').first.text
              })
              .with_image_config(->(ic) {
                ic.with_default_url('http://xn--80abzqel.xn--p1ai/')
              })
              .with_max_collection_length(300)
              .with_category_type('870')
              .with_category_type('957', 'ветровки')
              .with_category_type('957', 'куртки')
              .with_category_type('916', 'пуховики')
              .with_category_type('1865', 'жилеты')
              .with_category_type('1395', 'женская')
              .with_category_type('870', 'трикотаж')
              .with_category_type('120', 'рубашки')
              .with_category_type('30', 'футболки')
              .with_category_type('1395', 'спорт')
              .with_category_type('4679', 'джинсы')
              .with_category_type('877', 'шорты')
              .with_category_type('873', 'головные')
              .with_category_type('906', 'шарфы')
        }, Bayron::BayronProductConfig)
    end
  end
end