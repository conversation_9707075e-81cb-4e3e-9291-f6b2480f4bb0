# frozen_string_literal: true
module Sigil
  class SigilParser < Downloader

    def initialize
      @dlparams = {}
      @no_discount_cats=['coins','seriya-feyskaya-pyltsa','seriya-kosmicheskaya-pyltsa','kollektsiya-potal','sigil-eggs','sigil-books','sigil-adventures','rasprodazha']
      super
    end

    def get_name(name,cat)
      return name if cat.downcase.inlclude?('короб')
      return name if cat.downcase.inlclude?('румяна')

      return name.gsub('алитра ','алитра теней ') if cat.downcase.include?('палет') && !name.include?('тен')
      return name if name.downcase.include?('тен')

      "Тени для век #{name}"
    end

    def process_page(href)
      @log.info "process_page #{href}"
      page = @agent.get href

      cat = page.search('ul.breadcrumb span[itemprop=name]')[2..].map(&:text).join(' / ').gsub(' *','')
      name = page.at('h1.product-title').text

      script = page.body.match(/JCCatalogElement\((.+?)\);/).captures[0].to_s.gsub('\'', '"').gsub(' ', ' ')
                   .gsub('\'', '"').gsub(' ', ' ').gsub("\t"," ") #.gsub(/\s/, '')

      data = JSON.parse(script)

      @price_multiplier = 0.8


      desc = page.at('div[data-tab-target="DETAIL"]').text

      product_id = data['PRODUCT']['ID']

      pics = page.search('img[data-zoom]').map {|i| i.attr('data-zoom')}
      pics += page.search('div[data-tab-target="TAB_YROKI"] img').map {|i| i.attr('src')}
      pics.map! {|href| h=href; h="https://sigil.me#{href}" unless href.start_with?('http'); h}
      pics.uniq!

      dl_pics = []
      pics.each_with_index do |pic,i|
        dl_pics << savePic(pic, "#{product_id}~#{i}", true, true, false, :active, nil, show_order: i)
      end

      desc.gsub!(/\u00a0/, ' ')
      #desc.gsub!(/\xa0/, ' ')

      desc.gsub!(/(\r\n){2,}/,"\n\n")
      desc.gsub!("\t"," ")
      desc.gsub!(/ +/," ")
      desc.strip!

      brand = 'SIGIL Inspired Tammy Tanuka'

      cat_link = page.search('ul.breadcrumb a.breadcrumb-link').last.attr('href')
      if desc.include?('скидки не распространяются') || @no_discount_cats.any? { |c| cat_link.include?(c) }
        @price_multiplier = 1
      end

      desc.gsub!('Оптовые и СП-скидки не распространяются.','')

      col = addCollection(cat, 83924)

      name = get_name(name, cat)

      if data['OFFERS']
        data['OFFERS'].each do |offer|
          offer_id = offer['ID']
          var_id=offer['TREE']['PROP_15']
          var_name=data['TREE_PROPS'][0]['VALUES'][var_id]['NAME']
          price=offer['ITEM_PRICES'][0]['PRICE'].to_f
          rrp = price

          next if price==230

          price = (price*@price_multiplier).ceil
          rrp = nil if rrp == price

          @log.info "#{var_id} #{var_name}"
          addProduct(col, "#{product_id}-#{offer_id}", name, price, desc, [var_name], dl_pics, nil, 83924, nil, rrp: rrp, source: href, weight: 50, brand_name: brand)
        end
      else
        price=data['PRODUCT']['ITEM_PRICES'][0]['PRICE'].to_i
        rrp = price
        return if price==230

        price = (price*@price_multiplier).ceil

        rrp = nil if rrp == price
        addProduct(col, "#{product_id}", name, price, desc, [], dl_pics, nil, 83924, nil, rrp: rrp, source: href, weight: 50, brand_name: brand)
      end

    end
    def process_cat(href)
      @log.info "process_cat #{href}"
      page = @agent.get href
      page.search('.card-info').each do |div|
        next if div.at('.card-price').nil?
        next unless div.at('.card-price').text.downcase.include?('руб')
        next if div.at('.card-price').text.downcase.include?('нет в наличии')
        a = div.at('.card-title > a')
        process_page(a.attr('href'))
      end
    end
    def run
      before if @agent == nil

      page = @agent.get 'https://sigil.me/collection/all/?order=&page_size=96&PAGEN_1=1'
      h = page.search('a.pagination-link').last.attr('href')
      if h =~ /PAGEN_1=(\d+)/
        pages = $1.to_i
      else
        pages = 1
      end

      (1..pages).each do |page_num|
        process_cat("https://sigil.me/collection/all/?order=&page_size=96&PAGEN_1=#{page_num}")
      end


    end

  end
end