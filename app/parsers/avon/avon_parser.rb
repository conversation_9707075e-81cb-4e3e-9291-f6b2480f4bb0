module Avon
  class AvonParser < Downloader
    def initialize
      @dlparams={}

      super
    end

    def login
      @agent2=Mechanize.new
      page=@agent2.get 'https://www.avon.ru/REPSuite/loginMain.page?timeout=yes&not_ya=yes&previous_page=/fastOrder.page'
      login_form=page.form_with(:name=>'loginForm')
      login_form.userIdDisplay='41497386'
      login_form.password='olegka0'
      p=@agent2.submit(login_form, login_form.buttons.first)
      page=@agent2.get 'https://www.avon.ru/REPSuite/orderEntry.page'
      if %r!submitFormPao\('(\d+)/(\d+)'\)!=~page.body
        @curcomp=$2
        @curyear=$1
      end
    end

    def check_art(art,comp,year)
      @log.info "https://www.avon.ru/REPSuite/description.page?lineNum=#{art}&campaignNum=#{comp}&campaignYr=#{year}&action=loadProductAvail"
      page=@agent2.get "https://www.avon.ru/REPSuite/description.page?lineNum=#{art}&campaignNum=#{comp}&campaignYr=#{year}&action=loadProductAvail"
      @log.info page.body
      return true if page.body.include? 'YES' or page.body.include? 'YLLW'
      false
    end


    def get_weight(name, desc)
      t = name
      if name.downcase.include? 'набор'
        if /(набор.*?)код/m =~ desc.downcase
          t = "#{name}, описание: #{$1}"
        else
          t = "#{name}, описание: #{desc}"
        end
      end
      #puts t
      #nil
      ChatGpt.new.get_product_weight(t)
    end
    def processPage(href,catname)
      #href='https://my.avon.ru/tovar/8688'
      puts href
      @log.debug href
      begin
        page=@agent.get(href)
      rescue Mechanize::Error => e
        @log.debug "Mechanize error "
        return
      rescue Net::HTTPInternalServerError => exception
        @log.debug "Error 500"
        return
      end

      view_model=HTMLEntities.new.decode(page.search('[ng-init]').first.attr('ng-init').to_s)
      view_model.gsub!('ViewModel=','')
      view_model=JSON.parse(view_model)

      /var _ShopContext=({.*?});/=~page.body
      data=JSON.parse($1)

      name=view_model['Product']['Name']
      price=view_model['Product']['SalePrice']
      price+=0.01 if not price.nil?
      price=view_model['Product']['ListPrice'] if price.nil?
      desc=view_model['Description']
      desc+=' '+view_model['Ingredients'] if view_model['Ingredients']
      desc.gsub!(/<br ?\/>/,"\n")
      desc.gsub!(/<[^>]*>/,' ')
      desc.gsub!('&nbsp;',' ')
      desc.gsub!(/&.aquo;/,'"')

      desc.gsub!(/ +/,' ')
      desc.gsub!(/\n{2,}/,"\n\n")
      desc.strip!

      puts name
      puts price
      puts desc

      profile=view_model['Product']['ProfileNumber'].downcase

      #cdn_path=data['CdnPath']
      #cdn_path=data['CdnPaths'][0] if cdn_path.nil? and data['CdnPaths']
      #return if cdn_path.nil?
      cdn_path='https://my.avon.ru'

      pic_urls=(1..9).map {|i| "#{cdn_path}/assets/ru-ru/images/product/prod_#{profile}_#{i}_613x613.jpg"}

      group_name=''
      has_vars=false
        if view_model['HasShadeVariants'] or view_model['HasNonShadeVariants']
        group_name=name
        has_vars=true
        group_desc=desc
      end

      return if view_model['Product']['VariantGroups'][0].nil?

      view_model['Product']['VariantGroups'][0]['Variants'].each do |var|
        sku=var['DisplayLineNumber']
        puts sku
        pic2=nil
        if has_vars
          pic2=var['Image'] if view_model['HasShadeVariants']
        end

        if var['Name']!=''
          name=var['Name'].gsub(',','.') if var['Name']!=''
          puts name
        end

        col=addCollection(catname,0)

        name1=name
        sizes1=[]

        if has_vars
          #fields=[catname,sku,group_name,desc,price,name,'','',0]
          name1=group_name
          sizes1=[name]
        else
          size=view_model['Product']['PricePerUnitInformation']
          desc.strip!
          desc+='.' unless  desc.end_with? '.'
          desc+=" Объем/вес: #{size}"

          #fields=[catname,sku,name,desc,price,'','','',0]
        end
        pics=[]

        pic_urls.each_with_index do |url,i|
          begin
            p=savePic(url,"#{sku}~#{i}",true)
            pics<<p if p

            if i==0 and pic2
              p=savePic(pic2,"#{sku}~color",true)
              pics<<p if p
            end
          rescue Mechanize::ResponseCodeError
          end
        end


        prod=addProduct(col,sku,name1,price,desc,sizes1,pics,nil,0,nil,pos:0)
        unless check_art(sku,@curcomp,@curyear)
          prod.disabled=true
          prod.save
        end

        unless prod.weight
          prod.weight = get_weight(name1, desc)
          prod.save
        end
      end
      #exit
    end

    def processCat(href,catname)
      puts href
      %r!/([0-9-]+)/!=~href
      ids=$1.gsub('-',',')
      page=@agent.get "https://my.avon.ru/api/categoryapi/categorypagedata?ids=#{ids}&cmp=#{@campaign}&lng=ru"
      data=JSON.parse(page.body)

      data['Data']['Products'].each do |p|
        puts p['Id']
        @log.info p
        #puts p['Categories'][0]
        next if @found.include? p['Id']
        puts p['Categories'][0]
        next if p['Categories'][0].nil?
        next if p['IsNotAvailable']
        @found<< p['Id']
        uri='/tovar/'+p['Categories'][0]['Level2']['Id'].to_s+'-'+p['Id'].to_s+'/'+p['Categories'][0]['Level2']['Slug']+'/'+p['Slug']
        puts uri
        processPage(uri,catname)
        #exit
      end
      #@agent.download(src,fname) 

    end

    def run
      @found=[]

      login

      if @agent==nil then before end
      page=@agent.get 'https://my.avon.ru/'
      /var _ShopContext=({.*?});/=~page.body
      data=JSON.parse($1)

      @campaign=data['CampaignNumber']
      puts @campaign

      page=@agent.get "https://my.avon.ru/api/layoutapi/getheader?campaignNumber=#{@campaign}&isDesktop=true"

      cats=JSON.parse(page.body)
      cats['Data']['HeaderMenu']['Submenus'].each do |cat|
        next if cat['Text']=='Только Онлайн'
        cat['CategoryMenuItems'].each do |cat2|
          catname=cat['Text']+'-'+cat2['CategoryName']
          puts catname

          next if catname=='Уход за волосами-По типу волос'
          next if catname=='Уход за волосами-По линии'

          puts cat2['Url']
          processCat(cat2['Url'],catname)
        end
        puts cat['Url']
        processCat(cat['Url'],cat['Text'])

      end

    end

  end
end
