# frozen_string_literal: true
module Avon
  class AvonParser2 < Downloader

    def initialize
      @dlparams = {}
      @price_multiplier = 1

      super
    end


    def set_cat_data
      @cats = {
        "Правильное питание - Каши и Супы" => 108516,
        "Правильное питание - Кисели и Молочко" => 109989,
        "Правильное питание - Конфеты, Леденцы и Халва" => 108549,
        "Правильное питание - Напитки" => 108518,
        "Правильное питание - Отруб<PERSON>, Клетчатка, Крупа" => 108516,
        "Правильное питание - Растительные масла" => 100311,
        "Правильное питание - <PERSON>емена и Мука" => 100310,
        "Правильное питание - Специи, Приправы, Пряности" => 109362,
        "Правильное питание - Сушки" => 108516,
        "Правильное питание - Флаксы, Крекеры, Хлебцы" => 108516,
        "Натуральная косметика - Гиалуроновая косметика" => 108411,
        "Натуральная косметика - Детская косметика" => 109927,
        "Натуральная косметика - Для Волос" => 107353,
        "Натуральная косметика - Для кожи вокруг глаз" => 2330,
        "Натуральная косметика - Для Лица" => 83711,
        "Натуральная косметика - Для рук и ног" => 109580,
        "Натуральная косметика - Для Тела" => 83787,
        "Натуральная косметика - Уход за полостью рта" => 107847,
        "Пищевые добавки и БАДы - Водоросли" => 79550,
        "Пищевые добавки и БАДы - Детские БАДы" => 79550,
        "Пищевые добавки и БАДы - Капсулы" => 79550,
        "Пищевые добавки и БАДы - Коллаген" => 79547,
        "Пищевые добавки и БАДы - Лечебные масла" => 79550,
        "Пищевые добавки и БАДы - Пробиотики" => 79555,
        "Бытовая не химия - Большой объем" => 107180,
        "Бытовая не химия - Бальзамы для мытья посуды" => 107180
      }
    end

    def get_smell_text(data)
      r = []
      r << "#{data['smellText']}."
      if data['smellNoteText']
        r << "Верхние ноты: #{data['smellNoteText']['smellTop']}." if data['smellNoteText']['smellTop']
        r << "Сердце аромата: #{data['smellNoteText']['smellHeart']}." if data['smellNoteText']['smellHeart']
        r << "Базовые ноты: #{data['smellNoteText']['smellBasic']}." if data['smellNoteText']['smellBasic']
      end
      r
    end

    def get_pics(data, code)
      return nil if data.nil?

      urls = data.select {|d| d['place']=='SIZE_1000x1000'}.sort_by {|d| d['position']}.map {|d| d['url']}

      ret = []
      urls.each_with_index do |url, i|
        real_url=URI::Parser.new.escape("https://avon.ru/_next/image?url=#{url}&w=2048&q=75")
        p = savePic(real_url,"#{code}~#{i}", true)
        ret << p if p
      end
      ret.uniq {|p| p.datahash}
    end

    def get_product_cat(cat_name, sku)
      p = Product.where(purchase_id: @purchase.id, art: sku)[0]
      return p.category if p && p.category != 0
      c = Collection.where(purchase_id: @purchase.id, name: cat_name)[0]
      if c
        p = c.products.find {|pr| pr.category!=0}
        return p.category if p
      end
      if p
      c = p.collections[0]
        if c
          p = c.products.find {|pr| pr.category!=0}
          return p.category if p
        end
      end
      0
    end
    def process_page(variant, cat_name)
      @log.info variant
      ajax_headers = { 'X-Requested-With' => 'XMLHttpRequest', 'Accept' => '*/*', 'Rsc' => 1 }
      page = @agent.get("https://avon.ru/api/products?sku=#{variant['externalItemId']}&branchId=d760a1e2-90cd-4e11-8ac6-ec0221732a1d", [], nil, ajax_headers)
      #page = @agent.get("https://www.avon.ru/api/products?sku=570575&branchId=d760a1e2-90cd-4e11-8ac6-ec0221732a1d", [], nil, ajax_headers)
      # json_string = page.body.split("\r\n").find {|l| l.include?('defaultProductData')}
      data = JSON.parse(page.body)

      desc = data['siteDescription']
      if data['mainProperty']
        data['mainProperty'].each_with_index do |p,i|
          desc<<"#{p}: #{data['mainDescription'][i]}"
        end
      end

      if data['itemDescription'] && data['itemDescription']['usage']
        desc<<"Использование: #{data['itemDescription']['usage']['usageText'].join("; ")}.\n"
      end

      if data['itemDescription'] && data['itemDescription']['ingredients']
        if data['itemDescription']['ingredients']['ingredientsAction'] && data['itemDescription']['ingredients']['ingredientsAction'].count > 0
          desc<<"#{data['itemDescription']['ingredients']['ingredientsPrimary']}: #{data['itemDescription']['ingredients']['ingredientsAction'].join(', ').downcase}\n"
        else
          desc<<"#{data['itemDescription']['ingredients']['ingredientsPrimary']}.\n"
        end
      end

      if data['itemDescription'] && data['itemDescription']['perfumer']
        desc << "Парфюмер: #{data['itemDescription']['perfumer']['perfumerNameText']}, #{data['itemDescription']['perfumer']['perfumerDescriptionText']}."
      end

      if data['itemDescription'] && data['itemDescription']['smell']
        desc += get_smell_text(data['itemDescription']['smell'])
      end

      if data['ingredients'] && data['ingredients']['value']
        desc<<"Состав: #{data['ingredients']['value']}.\n"
      end

      desc_text = desc.join("\r\n").gsub('..','.')

      name = data['name']
      vol = data['sizeVolume']
      vol_unit = data['unit']
      w = data['weight'].to_f
      w *=1000 if w>0 && w<1
      w = 10 if w < 10
      weight = (w.to_i*1.1).round(-1)

      if data['variants'].count == 1
        return unless data['variants'][0]['canOrder']
        price = data['variants'][0]['sales']['specialPrice']
        rrp = data['variants'][0]['sales']['regularPrice']
        price = rrp if price.nil?
        rrp = nil if price == rrp

        stock = data['variants'][0]['stockQty']
        sku = data['variants'][0]['externalItemId']
        outer_code = data['variants'][0]['itemId']
        return unless stock > 0

        cat_id = get_product_cat(cat_name, sku)
        @log.info "#{cat_name} #{sku} #{cat_id}"
        pics = get_pics(data['variants'][0]['media'], outer_code)
        col = addCollection(cat_name, cat_id)
        size = "#{vol} #{vol_unit}" if vol && vol_unit
        addProduct(col, sku, name, price, desc_text, [size], pics, variant.to_json, cat_id, source: outer_code, weight: weight, rrp: rrp)

      elsif data['variants'].count > 1 && data['variants'].all? {|v| v['optionSize']}
        data['variants'].sort_by {|v| v['order']}.each do |v|
          next unless v['canOrder']
          price = v['sales']['specialPrice']
          rrp = v['sales']['regularPrice']
          price = rrp if price.nil?
          rrp = nil if price == rrp

          stock = v['stockQty']
          sku = v['externalItemId']
          outer_code = v['itemId']
          next unless stock > 0

          pics = get_pics(data['variants'][0]['media'], outer_code)
          size = v['optionSize']
          name2 = "#{name}, размер #{size}"
          cat_id = get_product_cat(cat_name, sku)
          @log.info "#{cat_name} #{sku} #{cat_id}"
          col = addCollection(cat_name, cat_id)
          addProduct(col, sku, name2, price, desc_text, [size], pics, variant.to_json, cat_id, source: outer_code, weight: weight, rrp: rrp)

        end
      elsif data['variants'].count > 1 && data['variants'].all? {|v| v['shadeName']}
        data['variants'].sort_by {|v| v['order']}.each do |v|
          next unless v['canOrder']
          price = v['sales']['specialPrice']
          rrp = v['sales']['regularPrice']
          price = rrp if price.nil?
          rrp = nil if price == rrp

          stock = v['stockQty']
          sku = v['externalItemId']
          outer_code = v['itemId']
          next unless stock > 0

          pics = get_pics(data['variants'][0]['media'], outer_code)

          cat_id = get_product_cat(cat_name, sku)
          @log.info "#{cat_name} #{sku} #{cat_id}"
          col = addCollection(cat_name, cat_id)
          name2 = "#{name}, цвет #{v['shadeName']}"
          size = "#{vol} #{vol_unit}" if vol && vol_unit
          addProduct(col, sku, name2, price, desc_text, [size], pics, variant.to_json, cat_id, source: outer_code, weight: weight, rrp: rrp)
        end
      end
      #puts data

    end

    def process_cat(name,  category, megamenuPart: nil, subcategory: nil)
      href = 'https://avon.ru/api/products/catalog'
      post_data = { branchId: "d760a1e2-90cd-4e11-8ac6-ec0221732a1d", category: category, page: 1 }
      post_data['megamenuPart'] = megamenuPart if megamenuPart
      post_data['subcategory'] = subcategory if subcategory

      loop do
        res = @agent.post(href, post_data.to_json, { 'Content-Type' => 'application/json' })
        d = JSON.parse(res.body)
        d['products'].each do |prod|
          prod['variants'].each { |variant| process_page(variant, name) }
        end
        break if post_data[:page] * 12 >= d['total']
        post_data[:page] += 1
      end
    end

    def load_catalog
      last_cats = ['dummy', 'drugoe', 'autlet', 'chernaya-pyatniza']

      res = @agent.get 'https://avon.ru/api/products/megamenu'
      d = JSON.parse(res.body)
      d['categories'].sort_by { |c| last_cats.find_index(c['nameLat']).to_i + 1 }.each do |c|
        if c['megaMenuPart']
          c['megaMenuPart'].each do |c1|
            c1['subCategories'].each do |c2|
              if c1['name'] == 'мужские ароматы'
                name = 'Мужская парфюмерия'
              else
                name = "#{c['name']} - #{c2['name']}"
              end
              process_cat(name, c['nameLat'], megamenuPart: c1['nameLat'], subcategory: c2['nameLat'])
            end
          end
        elsif c['subCategories']
          c['subCategories'].each do |c1|
            name = "#{c['name']} - #{c1['name']}"
            process_cat(name, c['nameLat'], subcategory: c1['nameLat'])
          end
        else
          process_cat(c['name'], c['nameLat'])
        end
      end

    end

    def run
      before if @agent == nil

      load_catalog

      #process_weight

      #      prompt = 'Ты современный маркетолог интернет-магазина, создай на основе описания новое описание, обязательно сохранив всю важную информацию. Не используй HTML-тэги. Красиво оформи все в связный текст. Включи эмоциональные триггеры, связанные с использованием товара.  Не будь излишне многословен. Разбей на абзацы. Не выделяй жирным. Сохрани всю информацию о составе, способе применения, энергетической ценности и БЖУ-составе'
      # ChatGptDescriptionBatch.new.run(@purchase_id, prompt: prompt)

    end

  end
end