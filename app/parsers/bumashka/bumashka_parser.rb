module Bumashka
  class BumashkaParser < Downloader
    #include InvoiceSimple

    def initialize
      @dlparams={'price_file'=>:file}

      super
    end



    def process_page(url,cat,price)
      @log.info(url)
      @agent.redirect_ok = true
      page = @agent.get(url)

      return if name=page.search('h1[data-hook="product-title"]').first.nil?

      art=url.split('/').last

      name=page.search('h1[data-hook="product-title"]').first.text
      desc1=page.search('pre[data-hook="description"]')
      desc2=page.search('pre[data-hook="info-section-description"]')

      desc=''
      desc=desc1.first.text if desc1.count>0
      desc+=' '+desc2.first.text if desc2.count>0


      desc.gsub!(/\.+/,'.')
      desc.gsub!(/ +/,' ')

      pics = []
      page.body.scan(/"fullUrl":"(https:.*?\.(jpg|png))/).each do |url|
        pics << url[0].gsub('\\','')
      end

      #if %r!<script type="application/ld\+json">(.*?)</script>!m=~page.body
      #  json=$1
      #  data=JSON.parse(json)
      #  pics=data['image']
      #end

       col = addCollection(cat, '107171')
       addProduct(col, art, name, price, desc, [], pics)
    rescue Mechanize::ResponseCodeError => exception
      puts exception
    end


    def process_file(file)
      book = RubyXL::Parser.parse(@price_file)
      cat=''
      book[0].sheet_data.rows.each_with_index do |row, k|
        if row[1] and row[1].value and (row[2].nil? or row[2].value.nil?)
          cat=row[1].value
          next
        end

        next if row[2].nil?

        href=row[2].value
        price=row[5].value

        next if price.to_i==0

        process_page(href,cat,price)
      end
    end

    def run
      before if @agent.nil?

      process_file(@price_file)

    end

  end
end
