require 'creek'

module StmlTea
  class StmlTeaParser < Downloader
    include InvoiceSimple

    attr_accessor :price_file

    def initialize
      @price_file=''
      @dlparams={'price_file'=>:file}
      @price_multiplier=1.25
      super
    end


    def process_file2(file)
      creek = Creek::Book.new file
      sheet = creek.sheets[0]


      sheet.rows_with_meta_data.each do |row|
        next if row['cells'].values[0].nil?
        name=row['cells'].values[0]
        if /(.*?) \((.*?)\), шт$/=~name
          name=$1
          art=$2
        else
          next
        end

        name.strip!

        price=row['cells'].values[1].to_f
        next if price==0
        price=(price*@price_multiplier).ceil

        cat_type=100362

        cat_type=108548 if name.include? "Кофе"
        cat_type=108546 if name.include? "молотый"
        cat_type=108545 if name.include? "зерн"
        cat_type=108547 if name.include? "раст"

        name.gsub!('oreka','oreca')

        col=addCollection('Новый товар',cat_type)
        rrp=(price.to_f*1.15).ceil
        p = addProduct(col,art,name,price, '',[],nil,nil,cat_type,nil,rrp:rrp)
        unless p.weight
          p.weight = ChatGpt.new.get_product_weight(p.name)
          p.save
        end

      end
    end

    def process_file(file)
      creek = Creek::Book.new file
      sheet = creek.sheets[0]

      cat_name=nil

      cols_old=[0,5,15]
      cols_new=[0,1,4]

      use_cols=cols_new
      use_cols=cols_old if sheet.rows.to_a[20].values[cols_old[2]].to_f>0



      sheet.rows_with_meta_data.each do |row|
        next if row['cells'].values[use_cols[0]].nil?
        art=row['cells'].values[use_cols[0]].to_s.strip

        name=row['cells'].values[use_cols[1]]

        if name.nil?
          cat_name=art
          next
        end

        name.strip!

        price=row['cells'].values[use_cols[2]].to_f
        next if price==0
        price=(price*@price_multiplier).ceil

        cat_type=100362

        cat_type=108548 if name.include? "Кофе"
        cat_type=108546 if name.include? "молотый"
        cat_type=108545 if name.include? "зерн"
        cat_type=108547 if name.include? "раст"

        desc=row['cells'].values[17].to_s.strip
        name=row['cells'].values[16].to_s.strip if row['cells'].values[16] and row['cells'].values[16].to_s.strip=''

        desc.gsub!(/цена за .*?,/i,'')

        pics=[]

        (18..24).each do |c|
          if row['cells'].values[c] and row['cells'].values[c].to_s.start_with? 'http'
            fn="#{art.gsub('/','_')}~#{c-18}"

            pics<<savePic(row['cells'].values[c].to_s,fn,true)
          end
        end


        name.gsub!('oreka','oreca')

        col=addCollection(cat_name,cat_type)
        rrp=(price.to_f*1.15).ceil
        p = addProduct(col,art,name,price,desc,[],pics,nil,cat_type,nil,rrp:rrp)
        unless p.weight
          p.weight = ChatGpt.new.get_product_weight(p.name)
          p.save
        end

      end
    end

    def run
      if @agent==nil then before end

      #process_file(@price_file)
      process_file2(@price_file)

    end

  end
end
