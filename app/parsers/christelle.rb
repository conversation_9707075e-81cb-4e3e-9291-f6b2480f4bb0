#encoding: utf-8

require 'downloader'
require 'spreadsheet'

class <PERSON><PERSON> < Downloader
  attr_accessor :price_file


  def initialize
    @dlparams={'price_file'=>:file}

    @subst={'couls'=>'coulls',
            'coullsverde'=>'coullsverd',
            'elegiagris'=>'elegiegris',
            'elegialemon'=>'elegielemon',
            'fleureblu'=>'fleurebleu',
            'gzel'=>'gzelika',
            'gzhelika'=>'gzelika',
            'lavanda'=>'lavender',
            'lavander'=>'lavender',
            'lecleare'=>'leclaire',
            'lilia'=>'lilie',
            'lotos'=>'lotus',
            'marakesh'=>'marokesh',
            'orhidea'=>'orchidea',
            'papillone'=>'papillon',
            'regata'=>'regate',
            'renessanscreme'=>'renessanscrme',
            'tatto'=>'tatoo',
            'toskana'=>'toscana'
    }

    @pics=Hash.new {|hash,key| hash[key]=[] }
    @descs=Hash.new

    super
  end

  def process_page(href)
    page = @agent.get(href)

    art=page.search('h1').text.strip

    art.gsub!(' ',' ')

    art=art.gsub(/ [A-Za-z]{1,2}$/,'').strip
    art.gsub('è','e')
    art=art.downcase
    fileart=art.gsub(/[^a-z]/,'')

    fileart=@subst[fileart] if @subst.include? fileart

    puts fileart

    cat=page.search('.site-path a')[1].text.strip
    cat='Скатерть' if cat=='Скатерти'
    cat='Фартук' if cat=='Фартуки'
    #puts cat

    desc=page.search('/html/body/div[1]/div[2]/div[2]/div/div/table/tbody/tr[1]/td[2]/p/text()').to_s
    desc=page.search('/html/body/div[1]/div[2]/div[2]/div/div/div[6]/p[1]/text()[1]').to_s if desc==''
    desc.gsub!(' ',' ')
    desc.gsub!(/^\.+/,'')
    desc.strip!
    #puts desc

    key=cat+'-'+fileart
    @log.info key
    pics=page.search("a.highslide").map{|a| a.attr('href').to_s}.uniq

    @pics[key]=pics
    @descs[key]=desc
  end



  def process_file(f)
    puts f
    book = Spreadsheet.open f

    sheet1 = book.worksheet 0

    sheet1.rows[13..-1].each_with_index do |row,i|

      if row.format(0).pattern_fg_color==:red then next end

      art=row[2]
      next if art.nil?
      art.strip!
      fileart=art.gsub(/ [A-Za-z]{1,2}$/,'').strip
      colname=fileart.dup

      fileart.gsub!('è','e')
      fileart.downcase!
      fileart.gsub!(/[^a-z]/,'')

      @log.info art
      @log.info fileart

      price=row[7]
      next if price.nil?

      @log.info price

      name=row[4]

      key=name+'-'+fileart

      name+=' '+row[5] if not row[5].nil?


      @log.info key

      next if not @pics.include? key and not name.include? 'Фартук'

      pics=[]

      if not @pics[key].nil?
        @pics[key].each_with_index do |pic,i|
            savePic(pic,"#{key}~#{i}",true)
            pics<<"#{key}~#{i}"
        end
      end

      @pics[key]=[]

      desc=@descs[key]

      desc=desc+". "+row[6].to_s if name.include? 'Салфетки'

      col=addCollection(colname,'1001')
      addProduct(col,art,name,price,desc,[],pics)

      @descs[key]=''

    end
  end


  def run
    if @agent==nil then before end
    @desc=Hash.new('')

    @urls=['http://christelle.ru/skaterti','http://christelle.ru/salfetki','http://christelle.ru/fartuki']
    @urls.each do |url|
      page=@agent.get(url)
      page.search("table.table1 a").each do |a|
        process_page(a.attr('href').to_s)
      end
    end


    process_file(@price_file)

    @purchase.reload
    @purchase.collections.each  {|col| col.destroy  if col.products.length<=1}
    @purchase.save

  end

end