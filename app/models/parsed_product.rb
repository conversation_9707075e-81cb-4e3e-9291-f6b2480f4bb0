#encoding: utf-8

# Класс-описание товара (для простоты дальнейшей обработки)
class ParsedProduct
  attr_accessor :category
  attr_accessor :articul
  attr_accessor :images
  attr_accessor :name
  attr_accessor :description
  attr_accessor :price

  def initialize(category, articul, images, name, description, price)
    # Категория товара. Берется из сайта
    @category = category || ''

    #Последняя часть урла
    @articul = articul || ''

    # Картинки добавляем из сайта
    @images = images || Array.new

    # Наименование товара. Уникальное, является ID
    @name = name || ''

    # Первая часть берется из Excel-файла
    # Вторая часть берется из описания на странице
    @description = description || ''

    # Из Excel
    @price = price || 0
  end

end

# Обрабатываем строку (убираем невидимые символы, лишние пробелы и прочая)
def prepare_string(arg_str)
  return if arg_str.nil?
  str = arg_str
  # Обрабатывает строку и убирает всякие двойные пробелы, табуляции и прочие вещи

  str.gsub!("\t", ' ') # иначе можно посклеивать слова, где они только табом разделены
  str.gsub!(/ +/,' ') # регэкспы наши большие друзья при обработке текста

  str.strip
end