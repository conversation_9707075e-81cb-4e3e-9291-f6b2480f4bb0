#encoding: utf-8

module InvoiceKataleya
  extend ActiveSupport::Concern

  #line from invoice
  # fields - one line from 100sp
  def invoice_comparator(line,fields)

    site_color=''
    site_size=''

    if /(\d+)/=~fields['Размер']
      site_size=$1
    end

    s=fields['Размер'].downcase.gsub('ё','е')
    if s.include? 'синий'
      site_color='синий'
    elsif s.include? 'черный'
      site_color='черный'
    elsif s.include? 'белый'
      site_color='белый'
    end

    color=''
    size=''
    name=''

    nm=line['Название']
    nm.gsub!('(плательная) синий','синий')
    nm.gsub!('(плательная) синий','синий')

    if /(.*?) +\((.*?)\) *\((.*?)\)$/=~nm
      name=$1
      color=$2
      size=$3
    elsif /(.*?) +\((.*?)\)$/=~nm
      name=$1
      size=$2
    end
    size.gsub!(/[рР .]/,'')

    color=color.upcase.gsub('Ё','Е').strip
    size=size.gsub(/\D/,'')
    size.strip!

    site_color.upcase!

    site_color=color if site_color==''

    name.upcase.gsub(/[^А-Я0-9]/,'')==fields['Наименование'].upcase.gsub(/[^А-Я0-9]/,'') and color==site_color  and size==site_size
  end

end