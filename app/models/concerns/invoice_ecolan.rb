#encoding: utf-8

module InvoiceEcolan
  extend ActiveSupport::Concern

  #line from invoice
  # fields - one line from 100sp
  def invoice_comparator(line,fields)
    #site_art=fields['Артикул'].gsub(/ .*/,'').strip.upcase
    site_name=fields['Наименование'].upcase.strip

    site_size=fields['Размер'].upcase.strip

    #site_size=''
    #if /^\S+ \d+ \d+ (.*)/=~fields['Артикул']
    #      site_size=$1.strip
    #end

    #inv_art=line['Артикул'].upcase.strip
    if /(.*?) \(([^(]*?)\)$/=~line['Название'].upcase.strip
      inv_name=$1
      inv_size=$2.gsub(',','.')

    end

    #inv_size=''
    #    if site_size!=''and /\(([^)]*)\)$/=~line['Название']
    #      inv_size=$1
    #end


    inv_name==site_name and inv_size==site_size
  end

end