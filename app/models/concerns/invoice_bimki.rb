#encoding: utf-8

module InvoiceBimki
  extend ActiveSupport::Concern

  #line from invoice
  # fields - one line from 100sp
  def invoice_comparator(line,fields)
    site_art=fields['Артикул'].upcase.strip

    art=line['Артикул'].gsub(/ .*/,'').upcase.strip

    size=''

    if line['Название'] and line['Название']=~/\(([^\(]*?), (.*?)\)$/
      size=$1
    elsif line['Размер'] and line['Размер']=~/([^\(]*?), (.*?)$/
      size=$1
    end

    art==site_art and size==fields['Размер']
  end

end