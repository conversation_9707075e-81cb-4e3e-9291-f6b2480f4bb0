#encoding: utf-8

module InvoiceMf
  extend ActiveSupport::Concern

  #line from invoice
  # fields - one line from 100sp
  def invoice_comparator(line,fields)
    site_name=fields['Наименование'].upcase.strip
    inv_name=line['Название'].upcase.strip

    site_size=fields['Размер']

    inv_size='-'
    if line['Название'] and line['Название']=~/(.*?) \(([^)]*?), (.*), (.*)\)$/
      inv_name=$1.upcase.strip
      color=$2
      size=$4
      inv_size="#{color}-#{size}"
    elsif line['Название'] and line['Название']=~/(.*?) \(([^)]*?), (.*)\)$/
        inv_name=$1.upcase.strip
        color=$2
        inv_size=color
    end


    if site_size=='-'
      site_name==inv_name
    else
      site_name==inv_name and site_size==inv_size
    end
  end

end