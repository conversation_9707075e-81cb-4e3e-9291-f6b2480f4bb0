class ApplicationController < ActionController::Base
  # Prevent CSRF attacks by raising an exception.
  # For APIs, you may want to use :null_session instead.
  rescue_from(StandardError, with: lambda { |e| handle_error(e) }) unless Rails.env.development?
  respond_to :json

  protect_from_forgery with: :exception, unless: -> { request.format.json? }

  def after_sign_in_path_for(resource)
    "/purchases#index"
  end

  def current_user
    #return User.find(1) if  not request.format=='json' #Rails.env.development? and
    super
  end

  before_action do
    if Rails.env.test?
      #Rack::MiniProfiler.authorize_request
    end
  end

  def render_jsonapi_response(resource)
    if resource.errors.empty?
      render jsonapi: resource
    else
      render jsonapi_errors: resource.errors, status: 400
    end
  end

  def handle_error(e)
    render json: {status: 'error', text: e.message, trace:e.backtrace[0..9].join("\n")}, status:500
  end
end
