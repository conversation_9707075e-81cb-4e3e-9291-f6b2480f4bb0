class ShipmentsController < ApplicationController
  before_action :set_shipment, only: [:show, :edit, :update, :destroy,:getcsv,:getmoyskladcsv]
  before_action :set_purchase, only: [:index]
  before_action :authenticate_user!

  def index

  end

  def show

  end

  def getcsv
    out=CSV.generate(:col_sep=>';') {|csv|
      csv<<['Коллекция','Артикул','Название','Подробнее','Цена','РРЦ','Размеры','UUID','Источник товара','Категория','Картинка','Картинка 1','Картинка 2','Картинка 3','Картинка 4','Картинка 5','Картинка 6']

    @shipment.shipment_items.each do |item|
      prod=item.product

      extra=prod.extra.to_s
      begin
        extra=JSON.parse(prod.extra)
        extra=extra['out'] if extra.is_a?(Hash) and extra['out']
        extra=extra.join '^' if extra.is_a?(Array)
      rescue
      end

      desc=ActionView::Base.full_sanitizer.sanitize(prod.desc)
      desc='' if desc.nil?

      desc.gsub!(/[\r\n]+/,'. ')
      desc.gsub!(/ +/,' ')
      desc.gsub!(/\.+/,'.')
      desc.strip!

      name=ActionView::Base.full_sanitizer.sanitize(prod.name)
      name='' if name.nil?

      name.gsub!(/[\r\n]+/,'. ')
      name.gsub!(/ +/,' ')
      name.gsub!(/\.+/,'.')
      name.strip!
      name.gsub!(/[.,]+$/,'.')

      price=prod.price.to_f.round.to_s.gsub('.0','')
      rrp=prod.rrp.to_s.gsub(/\s/,'')

      rrp='' if rrp.to_f*1.27<price.to_f

      cat=prod.category
      cat=prod.collections.first.coltype if cat==0 or cat.nil?

      if prod.collections.count==0
        if prod.collection_id
          coll=Collection.find prod.collection_id
          col_name=coll.name.gsub(/^!/,'')
          coll.add_product(prod)
        else
          col_name='__UNKNOWN__'
        end
      else
        col_name=prod.collections.first.name.gsub(/^!/,'')
      end


      line=[col_name.strip,prod.art.to_s.gsub(/ ###.*/,'').to_s.strip,name,desc,price,rrp,"-@#{item.amount}",'',extra,cat]

      urls=prod.get_image_urls
      line+=urls

=begin
      if prod.sizes and prod.sizes.start_with? '{"'
        size_data=JSON.parse(prod.sizes)
        size_data.each do |price,sizes|
          line[4]=price
          line[6]=sizes.map{|s| s.gsub(',','.')}.join(',')
          if sizes.count==1 and max_sizes<=1
            line[2]+=". Размер #{sizes.first}"
            line[2].gsub!(/\.+/,'.')
          end

          csv<<line
        end

      else
        csv<<line
      end
=end

      csv<<line
    end
    }

    send_data "\xEF\xBB\xBF"+out, filename: "#{@shipment.purchase.name}_поставка_#{@shipment.title}.csv", type: "text/csv"

  end

  private
  def set_shipment
    @shipment = Shipment.find(params[:id])
  end

  def set_purchase
    @purhcase = Purchase.find(params[:purchase_id])
  end

  # Never trust parameters from the scary internet, only allow the white list through.
  def shipment_params
    params[:shipment]
  end
end
