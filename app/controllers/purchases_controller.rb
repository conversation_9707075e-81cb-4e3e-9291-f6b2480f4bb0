require 'csv'

class PurchasesController < ApplicationController
  include SpAuthorize

  before_action :authenticate_user!

  load_and_authorize_resource

  skip_before_action :verify_authenticity_token

  def index
    respond_to do |format|
      #current_user=User.find 1 if current_user.nil?

      if params[:pp]
        @purchases = current_user.company.purchases.pp
      else
        @purchases = Purchase.accessible_by(current_ability).parsers
        #@purchases = current_user.purchases.parsers
      end

      format.json { render json: { purchases: @purchases } }
      format.html
    end
  end

  def show
    respond_to do |format|
      #current_user=User.find 1 if current_user.nil?

      @statuses = ['ничего не происходит', 'идет скачивание из источника', 'скачивание из источника завершено', 'идет загрузка на 100sp']

      @inventory_documents = InventoryDocument.where(doc_type: InventoryDocument.doc_types[:writeoff]).where("name like '%для%'").order(id: :desc)

      @purchase = Purchase.find(params[:id])

      col_ids = @purchase.collections.select(:id).map(&:id)

      brands = Product.where(disabled: false, collections_products: { collection_id: col_ids }).joins('INNER JOIN collections_products on products.id=collections_products.product_id').select(:brand_name).distinct.map(&:brand_name)

      @new_brands = brands.select { |b| b != nil and b != 'Укажите бренд' and SpBrand.find_by_name(b.gsub(' ', '').upcase).nil? }

      @products_updated = Product.joins(:purchase).where(purchase_id: @purchase.id).where('products.downloaded_at>purchases.download_started_at').count

      @max_products_in_collection = 0
      if @purchase.collections.count > 0
        @max_products_in_collection = @purchase.collections.max { |c1, c2|
          с1_count = 0
          c1_count = c1.active_products_count if c1 and c1.products
          с2_count = 0
          c2_count = c2.active_products_count if c2 and c2.products

          c1_count <=> c2_count
        }.active_products_count
      end

      @total_product_count = 0
      @active_product_count = 0
      @purchase.collections.each do |c|
        @total_product_count += c.products.count
        @active_product_count += c.active_products_count
      end

      @collection_renames = {}
      @purchase.collection_renames.each do |r|
        @collection_renames[r.new_name] = [] if @collection_renames[r.new_name].nil?
        @collection_renames[r.new_name] << r.old_name
      end

      #@product_count=0
      #@purchase.collections.each { |col| @product_count=@product_count+col.products.count}

      if @purchase.data.nil? or @purchase.data == '' then
        @purchase.data = Hash.new
      end

      authorize! :read, @purchase
      #if @purchase.user.id!=current_user.id then
      #  raise 'Error'
      #end

      begin
        ApiParser::ApiParser.is_a?(Class)
        @dl = Object.const_get(@purchase.dlclass).new
      rescue => e
        puts e.message
        @dl = nil
      end

      @dl.get_parameters(@purchase) if @dl.respond_to? :get_parameters

      @owned_tags = @purchase.owned_tags
      @owned_tags << 'Выгружается' unless @owned_tags.include? 'Выгружается'
      @owned_tags += @dl.purchase_tags.clone if @dl.purchase_tags
      @owned_tags += @purchase.collection_tags.to_s.split(',').map(&:strip)
      @owned_tags = @owned_tags.uniq.sort

      @purchase_variants=[]
      @purchase_variants=@dl.purchase_tags.clone if @dl.purchase_tags
      @purchase_variants+=@purchase.collection_tags.to_s.split(',').map(&:strip)
      @purchase_variants.uniq!

      begin
        @order = Object.const_get(@purchase.orderclass).new
      rescue
        @order = nil
      end
      @uploader = SpUpload.new

      format.json { render json: { purchase: @purchase, collections: @purchase.collections } }
      format.html
    end

  end

  def image_chooser
    @purchase = Purchase.find(params[:id])
    @no_pics = true if params[:show_only_wo_pics]
    if @no_pics
      @collections = @purchase.ordered_collections.select { |c| c.products.any? { |p| p.disabled == false and p.pictures.select { |p| p.active? }.count == 0 } }
    else
      @collections = @purchase.ordered_collections
    end
  end

  def send_image
    img = Picture.find(params[:id])
    send_file(img.path, disposition: 'inline')
  end

  def rename_collection
    cids = params[:cids]
    new_name = params[:new_name]
    @purchase = Purchase.find(params[:id])
    if @purchase.user.id != current_user.id then
      raise 'Error'
    end

    @purchase.rename_collection(cids, new_name, params[:create_rename_rule] == 'true')

    @collection_renames = {}
    @purchase.collection_renames.each do |r|
      @collection_renames[r.new_name] = [] if @collection_renames[r.new_name].nil?
      @collection_renames[r.new_name] << r.old_name
    end

    respond_to do |format|
      format.html {

        render(@purchase.ordered_collections)
      }
    end
  end

  def create_collection
    name = params[:name].strip
    @purchase = Purchase.find(params[:id])
    if @purchase.user.id != current_user.id then
      raise 'Error'
    end

    Collection.find_or_create_by!(purchase_id: @purchase.id, name: name)
    respond_to do |format|
      format.json { head :no_content }
    end
  end

  def delete_collection
    cid = params[:cid].strip
    @purchase = Purchase.find(params[:id])
    if @purchase.user.id != current_user.id then
      raise 'Error'
    end

    col = Collection.find(cid)
    if col.purchase.user.id != current_user.id then
      raise 'Error'
    end

    col.destroy!
    respond_to do |format|
      format.json { head :no_content }
    end
  end

  def showdata
    @purchase = Purchase.find(params[:id])
    if @purchase.user.id != current_user.id then
      raise 'Error'
    end
  end

  def load_candidate_pics
    purchase = Purchase.find(params[:id])
    active_pics = params[:active_pics] == 'on'

    data = params['file'].read.force_encoding('utf-8')
    img_dl = LoadCandidatePics.new
    if Rails.env.development?
      img_dl.before
      img_dl.run(purchase, data, active_pics)
      img_dl.after(nil)
    else
      img_dl.delay(:queue => 'main').run(purchase, data, active_pics)
    end
    redirect_to purchase_path(purchase), notice: 'Загрузка картинок начата'

  end

  def get_goods_without_pics
    purchase = Purchase.find(params[:id])
    products = purchase.get_all_products.select { |p| p.pictures.select { |p| not p.sample_pic? }.count == 0 }
    out = CSV.generate(:col_sep => ';') { |csv|
      csv << ['Артикул', 'Название']
      products.each { |p| csv << [p.art, p.name] }
    }

    send_data "\xEF\xBB\xBF" + out, filename: "#{purchase.name}_no_pics.csv", type: "text/csv"
  end

  def move_image
    image = Picture.find(params[:id])
    if params[:type] == 'active'
      image.active!
    elsif params[:type] == 'candidate'
      image.candidate!
    end
    image.save
  end

  def load_collection_csv
    @col_data = {}

    encoding = 'cp1251'
    begin
      CSV.foreach(params['file'].path, :headers => true, :col_sep => ';', :encoding => 'bom|utf-8', :liberal_parsing => true).first
      encoding = 'bom|utf-8'
    rescue
    end
    CSV.foreach(params['file'].path, :headers => true, :col_sep => ';', :encoding => encoding, :liberal_parsing => true) do |fields|
      @col_data[fields['Артикул']] = fields['Коллекция']
    end

    @purchase = Purchase.find(params[:id])
    @purchase.collections.each do |col|
      col.products.each do |prod|
        next if @col_data[prod.art].nil?
        c = Collection.where(purchase_id: @purchase.id, name: @col_data[prod.art]).first
        if c
          prod.collections = [c]
          prod.category = c.coltype
          prod.save
        else
          new_col = Collection.new
          new_col.name = @col_data[prod.art]
          new_col.coltype = prod.category
          new_col.purchase = @purchase
          new_col.save
          new_col.add_tag('Выгружается')
          prod.collections = [new_col]
          prod.save
        end
      end
    end
    redirect_to purchase_path(@purchase), notice: 'Данные имортированы'

  end

  def getmoyskladcsv
    @purchase = Purchase.find(params[:id])
    if @purchase.user.id != current_user.id then
      raise 'Error'
    end

    max_sizes = 0
    @purchase.products.each do |prod|
      if prod.sizes
        if prod.sizes.start_with? '{"'
          size_data = JSON.parse(prod.sizes)
          size_data.each do |price, sizes|
            max_sizes = sizes.count if sizes.count > max_sizes
          end
        else
          sizes = prod.sizes.split(',')
          max_sizes = sizes.count if sizes.count > max_sizes
        end
      end
    end

    out = CSV.generate(:col_sep => ';') { |csv|
      csv << ['Тип', 'Группы', 'Внешний код', 'Артикул', 'Наименование', 'Цена 100sp заказ', 'Валюта (Цена 100sp заказ)', 'Штрихкод EAN13', 'Изображение', 'Описание']
      @purchase.collections.order('pos,name').each do |col|
        col.products.each_with_index do |prod, idx|

          desc = ActionView::Base.full_sanitizer.sanitize(prod.desc)
          desc = '' if desc.nil?

          desc.gsub!(/[\r\n]+/, '. ')
          desc.gsub!(/ +/, ' ')
          desc.gsub!(/\.+/, '.')
          desc.strip!

          name = ActionView::Base.full_sanitizer.sanitize(prod.name)
          name = '' if name.nil?

          name.gsub!(/[\r\n]+/, '. ')
          name.gsub!(/ +/, ' ')
          name.gsub!(/\.+/, '.')
          name.strip!
          name.gsub!(/[.,]+$/, '.')

          price = prod.price.to_f.round.to_s.gsub('.0', '')

          col_name = col.name.gsub(/^!/, '')

          urls = prod.get_image_urls
          pic = urls.first.to_s

          line = ['Товар', "Самсон/#{col_name}".strip, prod.id, prod.art.to_s.gsub(/ ###.*/, '').to_s.strip, name, price, 'руб', prod.barcode, pic, desc]

          csv << line
        end
      end
    }

    send_data "\xEF\xBB\xBF" + out, filename: "#{@purchase.name}.csv", type: "text/csv"

  end

  def get_export_xlsx

    data = @purchase.get_export_data(use_stock: false,
                                     all_goods: params['all_goods'],
                                     use_one_sale_collection: params[:use_one_sale_collection],
                                     collections: params['collection_id'],
                                     max_products_per_collection: params[:max_products_per_collection].to_i,
                                     exclude_arts: params[:exclude_arts]

    )

    workbook = RubyXL::Workbook.new
    sheet = workbook.worksheets[0]

    data.each_with_index do |line, i|
      line.each_with_index { |d, k| sheet.add_cell(i, k, d) }
    end
    workbook.write(Dir.tmpdir + "/#{@purchase.name}_export.xlsx")
    send_file Dir.tmpdir + "/#{@purchase.name}_export.xlsx", type: "application/vnd.ms-excel"
  end

  def getcsv
    @purchase = Purchase.find(params[:id])

    use_stock = false
    use_stock = true if params[:use_stock]

    data = @purchase.get_export_data(use_stock: use_stock,
                                     all_goods: params['all_goods'],
                                     use_one_sale_collection: params[:use_one_sale_collection],
                                     collections: params['collection_id'],
                                     max_products_per_collection: params[:max_products_per_collection].to_i,
                                     exclude_arts: params[:exclude_arts]

    )

    out = CSV.generate(:col_sep => ';') { |csv|
      data.each do |line|
        csv << line
      end
    }
    send_data "\xEF\xBB\xBF" + out, filename: "#{@purchase.name}.csv", type: "text/csv"
    #render plain: out
  end

  def show_error_data
    @purchase = Purchase.find(params[:id])
    if @purchase.user.id != current_user.id then
      raise 'Error'
    end
  end

  def stop
    @purchase = Purchase.find(params[:id])
    if @purchase.user.id != current_user.id then
      raise 'Error'
    end
    @purchase.stop = true
    @purchase.save
    redirect_to purchase_path(@purchase), notice: 'Загрузка будет остановлена'
  end

  def load_stock_from_sp
    @purchase = Purchase.find(params[:id])

    @dl = LoadStockFromSp.new
    if Rails.env.development?
      @dl.before
      @dl.run(@purchase, params[:pid], params[:megaorder_id])
      @dl.after(nil)
    else
      @dl.delay(:queue => 'main').run(@purchase, params[:pid], params[:megaorder_id])
    end
    redirect_to purchase_path(@purchase), notice: 'Загрузка со 100sp начата'
  end

  def load_purchase_data
    @purchase = Purchase.find(params[:id])

    variant = params[:purchase_variant]

    job = LoadPurchaseData.new
    if params[:download]
      job.save(@purchase, params[:pid], variant: variant)
    elsif params[:upload_pics]
      job.upload(@purchase, params[:pid], only_pics: true, variant: variant)
    else
      job.upload(@purchase, params[:pid], variant: variant, purchase_num: params[:purchase_num])
    end

    redirect_to purchase_path(@purchase), notice: 'Данные загружены'
  end

  def load_col_pics_from_sp
    @purchase = Purchase.find(params[:id])

    @dl = LoadFromSp.new
    if Rails.env.development?
      @dl.before
      @dl.run(@purchase,
              params[:pid],
              params[:move_goods] == 'on',
              params[:change_good_type] == 'on',
              params[:move_disabled_goods_to_not_load] == 'on',
              params[:load_col_pics] == 'on',
              params[:load_groups] == 'on',
              params[:use_tag],
              params[:load_product_descriptions] == 'on'
      )
      @dl.after(nil)
    else
      @dl.delay(:queue => 'main').run(@purchase,
                    params[:pid],
                    params[:move_goods] == 'on',
                    params[:change_good_type] == 'on',
                    params[:move_disabled_goods_to_not_load] == 'on',
                    params[:load_col_pics] == 'on',
                    params[:load_groups] == 'on',
                    params[:use_tag]

      )
    end
    redirect_to purchase_path(@purchase), notice: 'Загрузка со 100sp начата'
  end

  def load_from_sp
    @purchase = Purchase.find(params[:id])

    replace = false
    replace = true if params[:replace] == '1'

    @dl = LoadPicsFromSp.new
    if Rails.env.development?
      @dl.before
      @dl.run(@purchase, params[:pid], replace)
      @dl.after(nil)
    else
      @dl.delay(:queue => 'main').run(@purchase, params[:pid], replace)
    end
    redirect_to purchase_path(@purchase), notice: 'Загрузка со 100sp начата'
  end

  def tag_collections
    @purchase = Purchase.find(params[:id])
    tag = params[:tag]

    params[:collections].each do |cid|
      col = Collection.find cid
      col.add_tag(tag)
    end
    respond_to do |format|
      format.json { head :no_content }
    end
  end

  def startdownload
    @purchase = Purchase.find(params[:id])

    if @purchase.user.id != current_user.id
      raise 'Error'
    end

    if @purchase.status == nil
      @purchase.status = 0
    end

    if @purchase.status != 0 and not Rails.env.development?
      raise 'Закупка обрабатывается'
    end

    @purchase.status = 10
    @purchase.message = 'Подготовка к обработке'
    @purchase.save

    ApiParser::ApiParser.is_a?(Class)

    #require @purchase.dlclass
    @dl = Object.const_get(@purchase.dlclass).new
    #if @dl.is_a? Framework::Parser
    @dl.purchase_id = params[:id]
    #else
    @dl.purchase = @purchase
    #end

    @purchase.data = Hash.new

    @dl.get_parameters(@purchase) if @dl.respond_to? :get_parameters

    params.each do |k, v|
      begin
        next unless @dl.dlparams.has_key? k
        if params[k].instance_of?(ActionDispatch::Http::UploadedFile)

          uploaded_io = params[k]
          filename = Rails.root.join('files', uploaded_io.original_filename)
          File.open(filename, 'wb') do |file|
            file.write(uploaded_io.read)
          end
          @dl.instance_variable_set('@' + k, filename)
        else
          @dl.instance_variable_set('@' + k, v)
          @purchase.data[k] = v
        end
      rescue
      end
    end

    @dl.clear_all = params[:clear_all_data]

    @purchase.save
    #@dl.run
    if Rails.env.development?
      @dl.before
      @dl.run
      @dl.success(nil)
      @dl.after(nil)
    else
      @dl.delay(:queue => 'main').run
    end
    redirect_to purchase_path(@purchase), notice: 'Загрузка начата'
  end

  def process_order
    @purchase = Purchase.find(params[:id])

    @dl = Object.const_get(@purchase.orderclass).new

    #prepare_purchase unless @dl.file_result
    if @purchase.user.id != current_user.id
      raise 'Error'
    end


    puts "Process order controller, params[id]=#{params[:id]}"
    @dl.purchase_id = params[:id]
    @dl.purchase = @purchase

    params.each do |k, v|
      begin
        next unless @dl.dlparams.has_key? k
        if params[k].instance_of?(ActionDispatch::Http::UploadedFile)

          uploaded_io = params[k]
          filename = Rails.root.join('files', uploaded_io.original_filename)
          File.open(filename, 'wb') do |file|
            file.write(uploaded_io.read)
          end
          @dl.instance_variable_set('@' + k, filename)
        else
          @dl.instance_variable_set('@' + k, v)
        end
      rescue
      end
    end

    @dl.agent_header = request.headers['User-Agent'] if @dl.need_agent_header

    @purchase.save
    if @dl.file_result
      d = @dl.run

      send_data(d[2], filename: d[0], type: d[1])
    else
      if Rails.env.development?
        @dl.before
        @dl.run
        @dl.after(nil)
      else
        @dl.delay(:queue => 'main').run
      end

      redirect_to purchase_path(@purchase), notice: 'Обработка заказа начата'
    end
  end

  def file
    fn = File.basename(params[:fn])

    disp = :attachment

    disp = :inline if fn.end_with? '.jpg' or fn.end_with? '.jpeg' or fn.end_with? '.png'

    send_file Rails.root.join('files', fn), { :disposition => disp }
  end

  def process_invoice
    ApiParser::ApiParser.is_a?(Class)
    @purchase = Purchase.find(params[:id])

    @dl = Object.const_get(@purchase.dlclass).new

    @invoice_data = []

    headers = {}
    ts = DateTime.now.strftime('%Y-%m-%d %H_%M')

    if params[:invoice_csv]
      params[:invoice_csv].each do |invoice_file|

        FileUtils.cp(invoice_file.path, Rails.root.join('files', "#{ts}_#{invoice_file.original_filename}"))

        if @dl.respond_to?(:read_invoice)
          d = @dl.read_invoice(invoice_file.path)
          @invoice_data += d
          headers = d.first.keys if d.first and headers.empty?
        else
          encoding = 'cp1251'
          begin
            CSV.foreach(invoice_file.path, :headers => true, :col_sep => ';', :encoding => 'bom|utf-8', :liberal_parsing => true).first
            encoding = 'bom|utf-8'
          rescue
          end
          CSV.foreach(invoice_file.path, :headers => true, :col_sep => ';', :encoding => encoding, :liberal_parsing => true) do |fields|
            headers = fields.to_h.keys
            next if fields['Артикул'].nil? and fields['Название'].nil? and fields['Штрихкод'].nil?
            @invoice_data << fields.to_h
            @invoice_data.last[:original_amount] = fields['Количество'].to_s.gsub(/\D/, '').to_i
          end
        end
      end
    end

    if @dl.respond_to?(:transform_invoice)
      @invoice_data = @dl.transform_invoice(@invoice_data)
    end

    unless params[:use_inventory_document].blank?
      doc = InventoryDocument.find params[:use_inventory_document]
      doc.inventory_document_lines.each do |line|
        t = @invoice_data.find { |i|
          if i['Размер']
            ret=i['Артикул'] == line.inventory_product.supplier_sku and i['Размер'].to_s == line.size.to_s
          else
            ret=i['Артикул'] == line.inventory_product.supplier_sku
          end
          ret
        }
        if t.nil?
          @invoice_data << { 'Артикул' => line.inventory_product.supplier_sku, 'Название' => line.inventory_product.name, 'Размер' => line.size, 'Количество' => -line.amount_change }
        else
          t['Количество'] = t['Количество'].to_i - line.amount_change.to_i
        end
      end
    end

    headers = @invoice_data.first.keys if headers.empty? and @invoice_data.first

    @result = Hash.new

    @confirm_count = Hash.new(0)

    params[:spfile_csv].each do |spfile|
      FileUtils.cp(spfile.path, Rails.root.join('files', "#{ts}_#{spfile.original_filename}"))

      csv_text = CSV.generate(encoding: 'utf-8', :col_sep => ';') do |csv|
        csv << ['#', 'Коллекция', 'Товар', 'Наименование', 'gid', 'Подробнее', 'Цена', 'Размер', 'Заказов', 'Подтверждено', 'Собрано', 'Недопоставлено', "Примечания к заказам", "Источник товара", "Картинка товара"]

        sep = ';'
        filedata = File.open(spfile.path, "r:utf-8", &:read)
        if /^sep=(.)/ =~ filedata
          sep = $1
        end

        CSV.parse(filedata, :skip_lines => /^sep=/, :headers => true, :col_sep => sep, :encoding => 'utf-8', :liberal_parsing => true) do |fields|
          ### samson hack ###
          pack_size = 1
          if (@purchase.dlclass == 'SamsonOld' or @purchase.dlclass == 'Simaland::SimalandParser') and /(\d{5,9})-(\d{1,3})/ =~ fields['Артикул']
            pack_size = $2.to_i
            fields['Артикул'] = $1
          end
          ########

          fields['Подтверждено'] = 0 if params[:clean_confirm]

          matching_lines = @invoice_data.select { |l| @dl.invoice_comparator(l, fields.to_h) }
          if matching_lines.count > 0
            matching_lines.each do |ml|
              if ml['Цена закуп'] and ml['Цена закуп'].to_f > 0 and params[:price_check]
                if ml['Цена закуп'].to_f > fields['Цена'].to_f
                  ml[:previous_price] = fields['Цена'].to_f
                  next
                end
              end

              if pack_size > 1
                packs_in_invoice = (ml['Количество'].to_s.gsub(/\D/, '').to_i.to_i / pack_size).to_i
                next if packs_in_invoice == 0
                add = fields['Заказов'].to_i - fields['Подтверждено'].to_i
                add = packs_in_invoice if packs_in_invoice < add
                add = 0 if add < 0

                fields['Подтверждено'] = fields['Подтверждено'].to_i + add
                ml['Количество'] = ml['Количество'].to_s.gsub(/\D/, '').to_i.to_i - add * pack_size
              else
                add = fields['Заказов'].to_i - fields['Подтверждено'].to_i
                add = ml['Количество'].to_i if ml['Количество'].to_s.gsub(/\D/, '').to_i.to_i < add
                add = 0 if add < 0

                fields['Подтверждено'] = fields['Подтверждено'].to_i + add
                @confirm_count[File.basename(spfile.original_filename)] += add
                ml['Количество'] = ml['Количество'].to_s.gsub(/\D/, '').to_i.to_i - add
              end

            end
          end
          csv << fields.to_h.values
        end
      end

      fn = File.basename(spfile.original_filename, '.csv') + '_result.csv'
      File.open(Rails.root.join('files', fn), 'w') { |f| f.write "\uFEFF" + csv_text }
      @result[File.basename(spfile.original_filename)] = "/assets/files/" + fn
    end

    @left_lines = 0
    @left_sum = 0
    csv_text = CSV.generate(encoding: 'utf-8', :col_sep => ';') do |csv|
      csv << headers
      @invoice_data.each do |l|
        l['Цена закуп'] = '0' if l['Цена закуп'].nil?
        next if l['Название'] == '502670 Мелкооптовая упаковка'
        if l['Количество'].to_i != 0
          csv << l.to_h.values
          @left_lines += 1
          @left_sum += l['Цена закуп'].strip.to_f * l['Количество'].to_i
        end
      end
    end
    ts = DateTime.now.strftime('%Y-%m-%d %H_%M')
    @left_fn = "#{@purchase.name}_invoice_result_#{ts}.csv"
    File.open(Rails.root.join('files', @left_fn), 'w') { |f| f.write "\uFEFF" + csv_text }

  end

  def create_shipment
    @purchase = Purchase.find(params[:id])

    raise 'Название поставки не может быть пустым' if params['shipment_name'].empty?

    shipment = Shipment.create(purchase: @purchase, title: params['shipment_name'])

    params['shipment_amount'].each do |art, amount|
      product = @purchase.find_product_by_art(art)
      raise "Товар не найден: #{art}" if product.nil?

      si = ShipmentItem.create(shipment: shipment, product: product, amount: amount, invoice_price: params['invoice_price'][art])
      shipment.shipment_items << si
    end
    shipment.save
    redirect_to purchase_path(@purchase), notice: 'Поставка сохранена'
  end

  def prepare_purchase
    if @purchase.user.id != current_user.id
      raise 'Error'
    end

    if @purchase.status == nil
      @purchase.status = 0
    end

    if @purchase.status != 0 and @purchase.status != 10 and not Rails.env.development?
      raise 'Закупка обрабатывается'
    end

    @purchase.status = 10
    @purchase.message = 'Подготовка к обработке'
    @purchase.save
  end

  def get_purchase_data
    @purchase = Purchase.find params[:id]

    result = {}
    result['message'] = @purchase.message
    last_product = @purchase.collections.last.try(:products).try(:last)
    result['upload_data'] = ''
    result['status'] = @purchase.status
    result['upload_data'] = last_product.created_at.localtime.strftime('%Y-%m-%d %H:%M:%S') unless last_product.nil?
    result['collections'] = @purchase.product_news.group_by { |p| p.cat }.map { |name, products|
      product = products.first
      begin
        cat_type = JSON.parse(product.cat_type)
      rescue
        cat_type = product.cat_type
      end

      { 'name' => name, 'count' => products.length, 'cat_type' => cat_type }
    }

    render json: result.to_json
  end

  #def joincollections
  #    @purchase= Purchase.find(params[:id])
  #    col1=Collection.find(params[:collection_id][0].to_i)
  #    params[:collection_id][1..-1].each do |col_id|
  #
  #    end
  #
  #  end

  def delete
    purchase = Purchase.find(params[:id])
    if purchase.user.id != current_user.id then
      raise 'Error'
    end
    purchase.destroy
    redirect_to action: "index"
  end

  def new
  end

  def create
    @purchase = Purchase.new
    @purchase.company = current_user.company
    @purchase.user = current_user
    @purchase.name = params[:name]
    @purchase.dlclass = params[:dlclass]
    @purchase.save

    redirect_to purchase_path(@purchase)
  end

  def save_purchase_info_text
    purchase = Purchase.find(params[:id])
    raise 'Error' if purchase.company.id != current_user.company.id
    text = params[:text]
    tag = params[:tag]

    text_type = params[:text_type]

    unless tag.blank?
      purchase.purchase_data[tag][text_type] = text
    else
      purchase.purchase_data[text_type] = text
    end
    purchase.save

  end

  def edit
    purchase = Purchase.find(params[:id])
    raise 'Error' if purchase.company.id != current_user.company.id
    purchase.name = params[:name]
    purchase.dlclass = params[:dlclass]
    purchase.orderclass = params[:orderclass]
    purchase.sp_brand_name = params[:sp_brand_name].to_s.strip
    purchase.skip_brands = params[:skip_brands].to_s.strip
    purchase.mega_id = params[:mega_id].to_s.strip
    purchase.code = params[:code].to_s.strip
    purchase.save
    redirect_to purchase_path(purchase)
  end

  def save_info
    purchase = Purchase.find(params[:id])
    raise 'Error' if purchase.user.id != current_user.id
    purchase.info = params[:info]
    purchase.save
    redirect_to info_purchase_path(purchase)
  end

  def startupload

    @purchase = Purchase.find(params[:id])

    if @purchase.user.id != current_user.id
      raise 'Error'
    end

    if params[:commit] == 'Скачать CSV'
      getcsv
    elsif params[:commit] == 'Скачать XLSX'
      get_export_xlsx
    elsif params[:commit] == 'Скачать CSV для МС'
      getmoyskladcsv
    else
      if params[:commit] == 'Загрузить картинки коллекций'
        @uploader = SpUploadCollectionImages.new
        @uploader.variant = params[:purchase_variant]
      else
        @uploader = SpUpload.new
      end

      if @purchase.user.id != current_user.id then
        raise 'Error'
      end
      #if @purchase.status!=0 and not Rails.env.development? then
      #  raise 'Закупка обрабатывается'
      #end

      #@purchase.status=10
      #@purchase.message='Подготовка к обработке'
      #@purchase.save

      up = Upload.new
      up.pid = params[:pid]
      up.uploaded_at = Time.now
      up.user = current_user
      up.save

      params.each do |k, v|
        begin
          @uploader.instance_variable_set('@' + k, v)
        rescue
        end
      end

      if Rails.env.development?
        @uploader.run(@purchase, params[:collection_id])
        @uploader.after(nil)
      else
        @uploader.delay(:queue => 'main').run(@purchase, params[:collection_id])
      end

      redirect_to purchase_path(@purchase), notice: 'Загрузка начата'
    end
  end

  def merge_collections
    purchase = Purchase.find(params[:id])
    ########## HACK

  end

  def getjson

    @purchase = Purchase.find(params[:id])

    if @purchase.user.id != current_user.id then
      raise 'Error'
    end

    @res = Hash.new
    @res['actions'] = []
    @res['name'] = @purchase.name
    action = { 'title' => 'Новая акция', 'provider_id' => 1, 'folders' => {} }
    @purchase.product_news.each do |prod|
      begin
        cat_type = JSON.parse(prod.cat_type)
      rescue
        cat_type = prod.cat_type
      end

      action['folders'][prod.cat] = { 'cat_type' => cat_type, 'products' => [] } if not action['folders'].has_key? prod.cat

      colors = JSON.parse(prod.colors)
      colors.each { |col, d|
        d['pics'] = d['pics'].map { |pic|
          if pic.is_a? Hash
            pic['path'] = "http://spup.primavon.ru/images/#{@purchase.dlclass.gsub('::', '_')}/#{pic['path']}.jpg" unless pic['path'].to_s.start_with? 'http'
          else
            pic = "http://spup.primavon.ru/images/#{@purchase.dlclass.gsub('::', '_')}/#{pic}.jpg" unless pic.to_s.start_with? 'http'
          end
          pic
        }
        unless d['color_image'].nil?
          d['color_image']['path'] = "http://spup.primavon.ru/images/#{@purchase.dlclass.gsub('::', '_')}/#{d['color_image']['path']}.jpg" unless d['color_image']['path'].to_s.start_with? 'http'
        end
      }
      sizes_table = JSON.parse(prod.sizes_table) unless prod.sizes_table.blank?
      sizes_table = [] if prod.sizes_table.blank?
      additional = JSON.parse(prod.additional) unless prod.additional.blank?
      additional = {} if prod.additional.blank?
      prod_hash = {
        name: prod.name,
        sku: prod.sku,
        desc: prod.desc,
        color: colors,
        additional: additional,
        sizes_table: sizes_table,
        sizes_link: prod.sizes_link,
        producer: prod.producer,
        composition: prod.composition
      }

      action['folders'][prod.cat]['products'] << prod_hash
    end
    @res['actions'] << action
    render json: @res.to_json
  end

  def show_error_file
    @purchase = Purchase.find(params[:id])
    if @purchase.data and @purchase.data['error_file']
      send_data("\xEF\xBB\xBF" + File.read(@purchase.data['error_file']), filename: "#{@purchase.name}_error.csv", type: 'appication/csv')
    end
  end

  def show_success_file
    @purchase = Purchase.find(params[:id])
    if @purchase.data and @purchase.data['success_file']
      if @purchase.data['success_file'].is_a? Array
        send_file(@purchase.data['success_file'][0])
      else
        send_data("\xEF\xBB\xBF" + File.read(@purchase.data['success_file']), filename: "#{@purchase.name}_result.txt", type: 'text/plain')
      end
    end
  end

  def show_dl_success_file
    @purchase = Purchase.find(params[:id])
    if @purchase.data and @purchase.data['dl_success_file']
      if @purchase.data['dl_success_file'].is_a? Array
        send_file(@purchase.data['dl_success_file'][0])
      else
        send_file(@purchase.data['dl_success_file'])
      end
    end
  end

  def get_json
    @purchase = Purchase.find(params[:id])

    if @purchase.user.id != current_user.id then
      raise 'Error'
    end

    folders = {}

    @purchase.product_news.each do |prod|
      begin
        cat_type = JSON.parse(prod.cat_type)
      rescue
        cat_type = prod.cat_type
      end

      folders[prod.cat] = { 'products' => {} } unless folders.has_key? prod.cat

      colors = JSON.parse(prod.colors)
      colors.each { |col, d|
        d['pics'] = d['pics'].map { |pic|
          if pic.is_a? Hash
            pic['path'] = "http://#{request.host_with_port}/images/#{@purchase.dlclass.gsub('::', '_')}/#{pic['path']}.jpg"
            pic
          else
            "http://#{request.host_with_port}/images/#{@purchase.dlclass.gsub('::', '_')}/#{pic}.jpg"
          end
        }
        d['pics'] = Hash[d['pics'].collect { |item| [item['hash'], item] }]

        unless d['color_image'].nil?
          d['color_image']['path'] = "http://#{request.host_with_port}/images/#{@purchase.dlclass.gsub('::', '_')}/#{d['color_image']['path']}.jpg"
        end

        d['sizes'] = remove_empty_keys(d['sizes'])
      }
      colors = remove_empty_keys(colors)
      sizes_table = JSON.parse(prod.sizes_table) unless prod.sizes_table.blank?
      sizes_table = [] if prod.sizes_table.blank?
      additional = JSON.parse(prod.additional) unless prod.additional.blank?
      additional = {} if prod.additional.blank?
      prod_hash = {
        title: prod.name,
        sku: prod.sku,
        description: prod.desc.blank? ? 'Без описания' : prod.desc,
        color: colors,
        additional: additional,
        sizes_table: sizes_table,
        sizes_link: prod.sizes_link,
        producer: prod.producer,
        composition: prod.composition,
        cat_type: cat_type
      }

      folders[prod.cat]['products'][prod.sku] = prod_hash
    end
    render json: folders.to_json
  end

  def info
    @purchase = Purchase.find(params[:id])

  end

  def remove_empty_keys(hash)
    return hash unless hash.has_key? ''

    result = hash.except('')
    result['default'] = hash['']
    result
  end


  private

  def request_params
    params.require(:request).permit(:name, :file, :manager_id, :spfile_csv, :fn, :invoice_csv, :tag, :collections, :cid, :new_name, :pic_file, :active_pics, :show_only_wo_pics, :all_goods, :price_check, :sids, :replace, :sp_brand_name, :use_inventory_document, :move_disabled_goods_to_not_load, :load_col_pics, :use_one_sale_collection, :code, :load_groups, :skip_brands, :mega_id)
    params.require(:purchase).permit(:name, :dlclass)
  end
end

