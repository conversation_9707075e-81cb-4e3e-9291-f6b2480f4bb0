const routes = [
  {
    path: "/",
    component: () => import("layouts/MainLayout.vue"),
    children: [
      { path: "", component: () => import("pages/IndexPage.vue") },
      { path: "/login", component: () => import("pages/LoginPage.vue") },
      {
        path: "/export_order",
        component: () => import("pages/ExportOrderPage.vue"),
      },
      {
        path: "/export_purchase",
        component: () => import("pages/ExportPurchasePage.vue"),
      },
      {
        path: "/copy_category",
        component: () => import("pages/CopyCategoryPage.vue"),
      },
      {
        path: "/rename_cols",
        component: () => import("pages/RenameColsPage.vue"),
      },
      {
        path: "/fix_pics",
        component: () => import("pages/FixPicsPage.vue"),
      },
      {
        path: "/pack_orders",
        component: () => import("pages/PackOrdersPage.vue"),
      },
      {
        path: "/purchase/:id/:collectionId?/:productId?",
        component: () => import("pages/PurchasePage.vue"),
      },
      {
        path: "/sima_sp",
        component: () => import("pages/SimaSpPage.vue"),
      },
      {
        path: "/sima_categories",
        component: () => import("pages/SimaCatMapPage.vue"),
      },
      {
        path: "/sima_orders",
        component: () => import("pages/SimaOrdersPage.vue"),
      },
      {
        path: "/ivanovo_orders",
        component: () => import("pages/IvanovoOrdersPage.vue"),
      },
      {
        path: "/sp_purchases",
        component: () => import("pages/SpPurchases.vue"),
      },
      {
        path: "/sp_cashier",
        component: () => import("pages/PpSalePage.vue"),
      },
    ],
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: "/:catchAll(.*)*",
    component: () => import("pages/ErrorNotFound.vue"),
  },
];

export default routes;
