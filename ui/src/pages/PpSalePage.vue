<script setup>
import {nextTick, ref, watch} from "vue";
import {api} from "boot/axios";
import {useQuasar} from "quasar";

const $q = useQuasar();

const gid = ref("")
const products = ref([])
const columns = [
  {name: "gid", field: "gid", label: "GID", align: "left"},
  {name: "sku", field: "sku", label: "Артикул", align: "left"},
  {name: "name", field: "name", label: "Название", align: "left"},
  {name: "price", field: "price", label: "Цена", align: "left"},
  {name: "sizes", field: "sizes", label: "Размеры", align: "left"},
  {name: "quantity", field: "quantity", label: "Количество", align: "left"},
  { name: 'actions', label: 'Actions', field: 'actions', align: 'center' }

]
const gidChanged = async (value) => {
  const v = value.trim()
  if (v=="") return

  const {data} = await api.get(`/api2/lookup_product_by_gid/${value}`);
  if (data) {
    data.quantity = 1;
    products.value.push(data)
    nextTick(() => {
      gid.value = ''
    });
  }

}

const sendToCashier = async () => {
  let gids = products.value.map( p => {return {gid: p.gid, quantity: p.quantity, price: p.price }})
  let res = await api.post("/api2/send_sp_products_to_cashier", {gids: gids});
  if (res.data.error) {
    $q.notify({
      message: res.data.error,
      color: 'negative',
      position: 'top',
      timeout: 5000,
    });
    return;
  }  else {
    $q.notify({
      message: 'Отправлено успешно',
      color: 'positive',
      position: 'top',
      timeout: 3000,
    });
  }
}

function  deleteRow(id) {
  const index = products.value.findIndex(row => row.id === id);
  if (index !== -1) {
    products.value.splice(index, 1);
  }
}
</script>

<template>
  <q-page padding class="q-gutter-md">
    <div class="row">
      <div class="col-8">
    <q-input v-model="gid" label="GID" @update:model-value="gidChanged"/>
      </div>
      <div class="col-2">
        <q-btn label="Очистить" icon="delete" @click="products=[]"/>
      </div>

      <div class="col-2">
        <q-btn class="primary" icon="save" label="Отправить в кассу" @click="sendToCashier"/>
      </div>

    </div>
    <q-table :rows="products" :columns="columns" :pagination="{rowsPerPage:0}" hide-bottom>
      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td v-for="col in props.cols" :key="col.name" :props="props">
            <template v-if="col.name === 'quantity'">
              <q-input
                dense
                v-model.number="props.row.quantity"
                type="number"
                min="0"
              ></q-input>
            </template>
            <template v-else-if="col.name === 'price'">
              <q-input
                dense
                v-model.number="props.row.price"
              ></q-input>
            </template>
            <template v-else-if="col.name === 'actions'">
              <q-btn dense flat icon="delete" @click="deleteRow(props.row.id)"></q-btn>
            </template>
            <template v-else>
              {{ col.value }}
            </template>
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </q-page>
</template>

<style scoped>

</style>
