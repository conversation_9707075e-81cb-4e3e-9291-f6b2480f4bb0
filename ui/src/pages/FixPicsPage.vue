<template>
  <q-page padding class="q-gutter-md">
    <div class="text-h6">Исправление картинок коллекций</div>
    {{ message }} {{ purchase?.status }}
    <q-input outlined v-model="pid" label="Номер закупки" style="width: 50vw" />

    <q-btn label="Начать" primary @click="startDownload" />
  </q-page>
</template>

<script>
// text file checkbox password textarea
import {
  defineComponent,
  getCurrentInstance,
  onMounted,
  onActivated,
} from "vue";
import { ref, watch } from "vue";
import { api } from "boot/axios";
import { useQuasar } from "quasar";
import { exportFile } from "quasar";

export default defineComponent({
  name: "ExportOrderPage",
  async setup() {
    const $q = useQuasar();
    const app = getCurrentInstance();
    const purchase = ref(null);
    const pid = ref("");
    const message = ref("");

    app.appContext.config.globalProperties.$$on("wsReceived", function (data) {
      if (purchase.value && data.purchaseId == purchase.value.id) {
        message.value = data.message;
        purchase.value.status = data.status;
      }
    });
g
    onMounted(() => {
      loadData();
    });

    async function loadData(id) {
      console.log(id);
      $q.loading.show({
        delay: 400, // ms
      });
      try {
        let res = await api.get(`/api3/purchases/199`);
        purchase.value = res.data.purchase;
        message.value = res.data.purchase.message;
      } catch (error) {
        $q.notify("Ошибка:" + error);
      }
      $q.loading.hide();
    }

    async function getResult() {
      const res = await api.get("/api3/purchases/213/show_dl_success_file", {
        responseType: "arraybuffer",
      });

      const status = exportFile(`export_${pid.value}.xlsx`, res.data, {
        //mimeType: mime,
      });
    }

    async function startDownload() {
      const data = { pid: pid.value };
      let res = await api.post(
        `/api3/purchases/${purchase.value.id}/startdownload`,
        data
      );
    }

    return { purchase, pid, startDownload, message, getResult };
  },

  components: {},
});
</script>
