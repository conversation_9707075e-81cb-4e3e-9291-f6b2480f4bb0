import { boot } from "quasar/wrappers";
import axios from "axios";
import { LocalStorage, Quasar, Loading, Notify } from "quasar";

// Be careful when using SSR for cross-request state pollution
// due to creating a Singleton instance here;
// If any client changes this (global) instance, it might be a
// good idea to move this instance creation inside of the
// "export default () => {}" function below (which runs individually
// for each client)

const protocol = window.location.protocol; // 'http:' или 'https:'
const host = window.location.hostname; // 'spupru.primavon.ru' или 'spup.primavon.ru'
const port = window.location.port ? `:${window.location.port}` : ""; // Если порт указан

const api = axios.create({
  baseURL:   process.env.NODE_ENV === "development" // false //
    ? "http://127.0.0.1:3000"
    :  `${protocol}//${host}`,
});
/*
const api = axios.create({
  baseURL: "https://spup.primavon.ru",
});
*/
export default boot(({ app, router }) => {
  // for use inside Vue files (Options API) through this.$axios and this.$api

  app.config.globalProperties.$axios = axios;
  // ^ ^ ^ this will allow you to use this.$axios (for Vue Options API form)
  //       so you won't necessarily have to import axios in each vue file

  app.config.globalProperties.$api = api;

  // ^ ^ ^ this will allow you to use this.$api (for Vue Options API form)
  //       so you can easily perform requests against your app's API
  api.interceptors.request.use(function (request) {
    console.log("req");
    const authHeader = LocalStorage.getItem("Auth_token");
    console.log(authHeader);
    if (authHeader) {
      request.headers["Authorization"] = authHeader;
    }
    return request;
  });

  api.interceptors.response.use(
    function (response) {
      return response;
    },
    function (error) {
      console.log(error.response.data);
      if (error.response.status === 401) {
        LocalStorage.remove("Auth_token");
        router.replace("/login");
        console.log("redirect");
        //window.location.replace("/#/login");
        return Promise.reject(error);
      } else {
        console.log(Loading);
        console.log(Loading.isActive);
        Loading.hide();

        Notify.create("Ошибка:" + error.response.data);

        console.log(error);
        throw error;
      }
    }
  );
});

export { api };
