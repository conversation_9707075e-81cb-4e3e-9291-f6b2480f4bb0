GIT
  remote: https://github.com/alex2b/creek.git
  revision: a4cb3c9af9d5084d4d0921d1ecb5de004642fd19
  specs:
    creek (2.2)
      http (~> 3.0)
      nokogiri (>= 1.7.0)
      rubyzip (>= 1.0.0)

GEM
  remote: https://rubygems.org/
  specs:
    actionmailer (4.2.10)
      actionpack (= 4.2.10)
      actionview (= 4.2.10)
      activejob (= 4.2.10)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 1.0, >= 1.0.5)
    actionpack (4.2.10)
      actionview (= 4.2.10)
      activesupport (= 4.2.10)
      rack (~> 1.6)
      rack-test (~> 0.6.2)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    actionview (4.2.10)
      activesupport (= 4.2.10)
      builder (~> 3.1)
      erubis (~> 2.7.0)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.3)
    activejob (4.2.10)
      activesupport (= 4.2.10)
      globalid (>= 0.3.0)
    activemodel (4.2.10)
      activesupport (= 4.2.10)
      builder (~> 3.1)
    activerecord (4.2.10)
      activemodel (= 4.2.10)
      activesupport (= 4.2.10)
      arel (~> 6.0)
    activesupport (4.2.10)
      i18n (~> 0.7)
      minitest (~> 5.1)
      thread_safe (~> 0.3, >= 0.3.4)
      tzinfo (~> 1.1)
    addressable (2.5.2)
      public_suffix (>= 2.0.2, < 4.0)
    anjlab-bootstrap-rails (*******)
      railties (>= 3.0)
      sass (>= 3.2)
    arel (6.0.4)
    bcrypt (3.1.12-x64-mingw32)
    builder (3.2.3)
    coderay (1.1.2)
    coffee-rails (4.0.1)
      coffee-script (>= 2.2.0)
      railties (>= 4.0.0, < 5.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    concurrent-ruby (1.0.5)
    crass (1.0.4)
    daemons (1.2.6)
    delayed_job (4.1.5)
      activesupport (>= 3.0, < 5.3)
    delayed_job_active_record (4.1.3)
      activerecord (>= 3.0, < 5.3)
      delayed_job (>= 3.0, < 5)
    devise (4.4.3)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0, < 6.0)
      responders
      warden (~> 1.2.3)
    domain_name (0.5.20180417)
      unf (>= 0.0.5, < 1.0.0)
    erubis (2.7.0)
    eventmachine (1.2.7-x64-mingw32)
    execjs (2.7.0)
    globalid (0.4.1)
      activesupport (>= 4.2.0)
    gon (6.2.0)
      actionpack (>= 3.0)
      multi_json
      request_store (>= 1.0)
    hike (1.2.3)
    htmlentities (4.3.4)
    http (3.3.0)
      addressable (~> 2.3)
      http-cookie (~> 1.0)
      http-form_data (~> 2.0)
      http_parser.rb (~> 0.6.0)
    http-cookie (1.0.3)
      domain_name (~> 0.5)
    http-form_data (2.1.0)
    http_parser.rb (0.6.0)
    i18n (0.9.5)
      concurrent-ruby (~> 1.0)
    jbuilder (1.5.3)
      activesupport (>= 3.0.0)
      multi_json (>= 1.2.0)
    jquery-rails (4.3.3)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.1.0)
    loofah (2.2.2)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.7.0)
      mini_mime (>= 0.1.1)
    mechanize (2.7.5)
      domain_name (~> 0.5, >= 0.5.1)
      http-cookie (~> 1.0)
      mime-types (>= 1.17.2)
      net-http-digest_auth (~> 1.1, >= 1.1.1)
      net-http-persistent (~> 2.5, >= 2.5.2)
      nokogiri (~> 1.6)
      ntlm-http (~> 0.1, >= 0.1.1)
      webrobots (>= 0.0.9, < 0.2)
    method_source (0.9.0)
    mime-types (3.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2016.0521)
    mini_mime (1.0.0)
    mini_portile2 (2.3.0)
    minitest (5.11.3)
    multi_json (1.13.1)
    mysql2 (0.5.1-x64-mingw32)
    net-http-digest_auth (1.4.1)
    net-http-persistent (2.9.4)
    nokogiri (1.8.2-x64-mingw32)
      mini_portile2 (~> 2.3.0)
    ntlm-http (0.1.1)
    orm_adapter (0.5.0)
    pg (1.0.0-x64-mingw32)
    pry (0.11.3)
      coderay (~> 1.1.0)
      method_source (~> 0.9.0)
    public_suffix (3.0.2)
    rack (1.6.10)
    rack-test (0.6.3)
      rack (>= 1.0)
    rails (4.2.10)
      actionmailer (= 4.2.10)
      actionpack (= 4.2.10)
      actionview (= 4.2.10)
      activejob (= 4.2.10)
      activemodel (= 4.2.10)
      activerecord (= 4.2.10)
      activesupport (= 4.2.10)
      bundler (>= 1.3.0, < 2.0)
      railties (= 4.2.10)
      sprockets-rails
    rails-deprecated_sanitizer (1.0.3)
      activesupport (>= 4.2.0.alpha)
    rails-dom-testing (1.0.9)
      activesupport (>= 4.2.0, < 5.0)
      nokogiri (~> 1.6)
      rails-deprecated_sanitizer (>= 1.0.1)
    rails-html-sanitizer (1.0.4)
      loofah (~> 2.2, >= 2.2.2)
    railties (4.2.10)
      actionpack (= 4.2.10)
      activesupport (= 4.2.10)
      rake (>= 0.8.7)
      thor (>= 0.18.1, < 2.0)
    rake (12.3.1)
    rdoc (6.0.4)
    request_store (1.4.1)
      rack (>= 1.4)
    responders (2.4.0)
      actionpack (>= 4.2.0, < 5.3)
      railties (>= 4.2.0, < 5.3)
    ruby-ole (********)
    rubyXL (3.3.29)
      nokogiri (>= 1.4.4)
      rubyzip (>= 1.1.6)
    rubyzip (1.2.1)
    sass (3.2.19)
    sass-rails (4.0.5)
      railties (>= 4.0.0, < 5.0)
      sass (~> 3.2.2)
      sprockets (~> 2.8, < 3.0)
      sprockets-rails (~> 2.0)
    sdoc (1.0.0)
      rdoc (>= 5.0)
    spreadsheet (1.1.7)
      ruby-ole (>= 1.0)
    sprockets (2.12.4)
      hike (~> 1.2)
      multi_json (~> 1.0)
      rack (~> 1.0)
      tilt (~> 1.1, != 1.3.0)
    sprockets-rails (2.3.3)
      actionpack (>= 3.0)
      activesupport (>= 3.0)
      sprockets (>= 2.8, < 4.0)
    thin (1.7.2)
      daemons (~> 1.0, >= 1.0.9)
      eventmachine (~> 1.0, >= 1.0.4)
      rack (>= 1, < 3)
    thor (0.20.0)
    thread_safe (0.3.6)
    tilt (1.4.1)
    tzinfo (1.2.5)
      thread_safe (~> 0.1)
    tzinfo-data (1.2018.5)
      tzinfo (>= 1.0.0)
    uglifier (4.1.10)
      execjs (>= 0.3.0, < 3)
    unf (0.1.4)
      unf_ext
    unf_ext (*******-x64-mingw32)
    unicode_utils (1.4.0)
    warden (1.2.7)
      rack (>= 1.0)
    webrobots (0.1.2)

PLATFORMS
  x64-mingw32

DEPENDENCIES
  anjlab-bootstrap-rails (>= *******)
  coffee-rails (~> 4.0.0)
  creek!
  daemons
  delayed_job_active_record
  devise
  gon
  htmlentities
  jbuilder (~> 1.2)
  jquery-rails
  json
  mechanize
  mysql2 (~> 0.5)
  pg
  phashion
  pry
  rails (~> 4.2.4)
  rmagick
  rubyXL
  rubyntlm
  rubyzip
  sass-rails (~> 4.0.0)
  sdoc
  spreadsheet
  therubyracer
  thin
  tzinfo-data
  uglifier (>= 1.3.0)
  unicode_utils

BUNDLED WITH
   1.16.2
