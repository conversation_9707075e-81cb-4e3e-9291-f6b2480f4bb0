source 'https://rubygems.org'

#gem 'eventmachine', :platforms => :ruby

#require 'em/pure_ruby'

# Bundle edge Rails instead: gem 'rails', github: 'rails/rails'
gem 'rails' , '*******'

gem 'bootsnap', require: false

# Use mysql as the database for Active Record


# Use SCSS for stylesheets
#gem 'sass-rails', '~> 5.0.0'

## Use Uglifier as compressor for JavaScript assets
gem 'uglifier', '>= 1.3.0'

# Use CoffeeScript for .js.coffee assets and views
gem 'coffee-rails' #, '~> 4.0.0'

# See https://github.com/sstephenson/execjs#readme for more supported runtimes
#gem 'therubyracer', platforms: :ruby
#gem 'mini_racer', platforms: :ruby

#gem 'sprockets' , '~> 3.0.0'

# Use jquery as the JavaScript library
gem 'jquery-rails'

# Turbolinks makes following links in your web application faster. Read more: https://github.com/rails/turbolinks
#gem 'turbolinks'

# Build JSON APIs with ease. Read more: https://github.com/rails/jbuilder
#gem 'jbuilder', '~> 1.2'

group :doc do
  # bundle exec rake doc:rails generates the API under doc/api.
  #gem 'sdoc', require: false
end

# Use ActiveModel has_secure_password
# gem 'bcrypt-ruby', '~> 3.0.0'

# Use Capistrano for deployment
# gem 'capistrano', group: :development

# Use debugger
# gem 'debugger', group: [:development, :test]

#gem 'anjlab-bootstrap-rails', '>= *******', :require => 'bootstrap-rails'
gem 'bootstrap', '~> 4.5.0'

#gem 'gon'

gem 'devise'
gem 'devise-jwt'

gem 'delayed_job_active_record'

gem 'mechanize'

gem 'spreadsheet'

gem 'json'

gem 'pg'

#gem 'thin'

gem 'htmlentities'

gem 'rubyXL' #, github: 'weshatheleopard/rubyXL', branch: 'drawing_support'

gem 'rubyzip'

gem 'tzinfo-data'

gem 'rmagick'

platforms :ruby do
  gem 'phashion'
    #gem 'mysql2', '~> 0.4.10'
end

platforms :x64_mingw do
  gem 'rubyntlm'
end

group :development do
  gem "better_errors"
  gem "binding_of_caller"
  gem 'active_record_query_trace'
  gem 'rubocop'
end

gem 'vcr'
gem 'webmock'

gem 'pry'

gem 'creek'#, :git=> 'https://github.com/alex2b/creek.git'

gem 'csv', '3.1.7'
#gem 'rakuten_web_service'

#gem 'acts-as-taggable-on', '~> 6.0'

gem 'font-awesome-sass', '~> 5.12.0'

#gem 'yml_builder', path: "c:/users/<USER>/RubymineProjects/yml_builder"
gem 'yml_builder', git: 'https://github.com/alex2b/yml_builder.git'

gem 'puma'

gem 'pg_search'

gem 'webpush'

group :test do
  #gem 'rack-mini-profiler'
  #gem 'flamegraph'
  #gem 'stackprof'
  gem 'parallel'
end

gem 'concurrent-ruby', require: 'concurrent'


gem 'jwt'
gem 'rack-cors'

#gem 'net-http-persistent', '4.0.1', git: 'https://github.com/drbrain/net-http-persistent'

gem 'connection_pool'

gem 'webrick'
gem 'rexml'

gem 'whenever', require: false

gem "mongo", "~> 2"

gem 'cancancan'

gem 'redis', '4.6.0'
gem 'redis-queue'

gem 'lightly'

#gem 'sidekiq'

gem "mini_magick"

gem 'pony'

#gem 'puppeteer-ruby'

gem 'jsonapi-rails'

gem 'net-smtp'

gem "daemons"

gem "warden-jwt_auth", '0.6.0'

gem "open3"

gem 'http'

gem 'roo'
gem 'roo-xls'

gem 'selenium-webdriver', '~> 4.26'

gem "ruby-openai", '>= 7.0.0'

gem 'telegram-bot-ruby'

#gem 'libxml-ruby'
gem 'ox'

gem "anthropic"

#gem 'oj'
gem 'nokogiri', '1.16'

gem 'amatch'