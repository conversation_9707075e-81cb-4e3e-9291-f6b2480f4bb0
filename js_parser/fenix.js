//const parseCsv = require('csv-parse/lib/sync')
const axios = require("axios")
const cheerio = require("cheerio")
//const stripHtml = require("string-strip-html")
//const {addProduct, addProducts} = require("./api")

//var xmlParser = require('xml2js')
//var fs = require('fs')

const {sleep, get, post, getColNumber,getFullUrl, useCache} = require('./parsetools')
const {addProduct, addMessage} = require("./api");

useCache(false)

let products=[]
let productsInFolder=new Map()

function guessCat(cat,name) {
    cat=cat.toLowerCase()
    name=name.toLowerCase()

    let ret=null
   
 if (cat.includes("ежед") || name.includes("ежед")) ret=100396 // ежедневники
    if (cat.includes("блокн") || name.includes("блокн")) ret=100396 // блокноты
    if (cat.includes("альб") || name.includes("альб")) ret=100396 // альбомы для рисования
    if (cat.includes("офис") && ret==null) ret=101022 // офисные принадлежности
    if (cat.includes("книг") || cat.includes("книж")) ret=101034 // книги
    if (cat.includes("школ") ) ret=1387 // школьные принадлежности

    if (ret==null) ret=101022
    return ret
}

async function login() {
    await get('https://phoenix-plus.ru')
    const data={'backurl':'/catalog/',
        'AUTH_FORM': 'Y',
        'AJAX-ACTION': 'AUTH',
        'TYPE': 'AUTH',
        'USER_LOGIN': '<EMAIL>',
        'USER_PASSWORD': '270279',
        'OTP_REMEMBER': 'Y'}
    const res=await post('https://phoenix-plus.ru/auth/',data,false)
}

async function processProduct(href,rrp) {
    console.log(href)
    addMessage(href,246)
    try {

        const $=await get(href)

        if ($('.product-info [data-pack]').length!=1) return

        const sku=$(".product-info-mobile .product-article").text().replace(/арт\./g,"").trim()
        let barcode=""
        const name=$('.product-info-mobile .product-description').text().trim()
        let pictures=$(".product-photos img").map((i,el)=>getFullUrl(el.attribs["src"])).toArray()
        pictures=pictures.filter((value, index, self) =>self.indexOf(value) === index)

        const buyPrice=Math.ceil(parseFloat($("span.product-price-current").text().replace(/[^.0-9]/g,"")))
        const price=Math.ceil(parseFloat($("span.product-price-current").text().replace(/[^.0-9]/g,""))*1.05)
        const minOrder=$('.product-info [data-pack]')[0].attribs["data-pack"]

        let s="-"
        if (minOrder!="1") s=`Уп. ${minOrder} шт, цена за 1`

        let prices={}
        prices[price]={rrp: rrp, sizes: [s],buy_price:buyPrice} //`Цена за упаковку ${minOrder} шт`

        let cats=[]
        for (const el of $("div[itemprop=breadcrumb] a span")) {
            const t=$(el).text().trim()
            if (t=="Главная"||t=="Каталог") continue
            cats.push(t)
        }

        let description=$(".product-features-content #t2 .product-features-content-item-inner").text().trim()

        let props=new Map()
        for (const row of $(".product-features-content #t1 .row")) {
            const k=$(row).find(".dt").text().toLowerCase()
            const v=$(row).find(".dd").text()
            props.set(k,v)
        }
        let brand=props.get("бренд")
        if (!brand) brand="Феникс+"
        props.delete("бренд")

        let props3=new Map()
        for (const row of $(".product-features-content #t3 .row")) {
            const k=$(row).find(".dt").text().toLowerCase()
            const v=$(row).find(".dd").text()
            props3.set(k,v)
        }
        if (props3.get("штрихкод единицы товара")) {
            barcode=props3.get("штрихкод единицы товара")
        }

        let propsText=Array.from(props).map(([k,v])=>`${k}: ${v}`).join(", ")

        description+=". "+propsText
        description=description.replace(/\.+/g,".")

        const product = {
            sku,
            source: href.replace("/catalog/",""),
            prices: prices,
            name: name,
            folderTree: cats,
            pictures,
            description,
            cat_id: guessCat(cats.join("-"),name),
            brand,
            barcode
        }
        products.push(product)
        if (!productsInFolder.has(cats.join("~"))) {
            productsInFolder.set(cats.join("~"),0)
        }
        productsInFolder.set(cats.join("~"),productsInFolder.get(cats.join("~"))+1)

    }catch (e) {
        addMessage(e)
    }

}

let productInfo=new Map()

async function processCat(href) {
    console.log("Cat "+href)
    const $=await get(href+"?perpage=27")

    for (const div of $(".catalog-item-inner")) {
        const productHref=$(div).find("a.catalog-item-link")[0].attribs["href"]
        const t=$(div).find("span.catalog-item-name-pre").text().replace(/арт\. +/i,"").replace(/,(.|[\r\n])*/g,"")
        const rrp=Math.ceil(parseFloat($(div).find("span.catalog-item-price-current").text().replace(/[^.0-9]/g,"")))
        console.log(href)
        console.log(t)
        console.log(rrp)
        productInfo.set(productHref,rrp)
    }

    const nextHref=$("a.pager-item-last")
    if (nextHref.length>0) {
        await processCat(nextHref[0].attribs["href"])
    }
}

function mergeFolders() {
    for (let i=0;i<6;i++) {
        for (let p of products) {
            if (p.folderTree.length>1 && productsInFolder.get(p.folderTree.join("~"))<30) {
                productsInFolder.set(p.folderTree.join("~"),productsInFolder.get(p.folderTree.join("~"))-1)
                p.folderTree.pop()
                if (!productsInFolder.has(p.folderTree.join("~"))) productsInFolder.set(p.folderTree.join("~"),0)
                productsInFolder.set(p.folderTree.join("~"),productsInFolder.get(p.folderTree.join("~"))+1)
            }
        }
    }
    for (let p of products) {
        p.folder=p.folderTree.join(" - ")
        p.folder=p.folder.replace("НОВЫЙ ГОД - НОВЫЙ ГОД СУВЕНИРЫ. УПАКОВКА. ПОСУДА 2022 - ","")
        p.folder=p.folder.replace("ПОДАРОЧНАЯ УПАКОВКА - ","")
        p.folder=p.folder.replace("ПРАЗДНИЧНАЯ ПРОДУКЦИЯ - ","")
        p.folder=p.folder.replace("СУВЕНИРЫ И ПОДАРКИ - ","")
    }
}


function getCatalogTree($) {
    let ret=new Map()
    for (const a of $('a[href^="/catalog"]')) {
        const href=a.attribs['href']
        let route=href.split("/").filter((el)=>el!='' && el!="catalog")
        const name=$(a).find("span[class$=name-title]").text()
        const itemCount=parseInt($(a).find("span[class$=add]").text().replace(/[()]/g,""))

        ret.set(route.join("~"),{itemCount,href,name,route})
    }

    for (const [path,data] of ret) {
        if (data.route.length>1) {
            for (let i=1;i<data.route.length;i++) {
                //data.route.slice(0, i).join("~")
                //ret.get(data.route.slice(0, i).join("~"))
                ret.delete(data.route.slice(0, i).join("~"))

            }
        }
        if (path.includes("aktsiya")) ret.delete(path)
        if (path.includes("torgovoe_oborudovanie")) ret.delete(path)
    }
    console.log(ret)
    return ret
}

async function parse() {
    const $=await get("https://phoenix-plus.ru/ajax/getMenuSections.php")
    let cats=getCatalogTree($)
    let i=0
    for (let [_,cat] of cats) {
        await processCat(cat.href)
        i++
        //if (i==1) break
    }
    await login()

    for (let [url,rrp] of productInfo) {
        await processProduct(url,rrp)
    }

    mergeFolders()
    for (let p of products) {
        await addProduct(p,246)
    }

//    const res = await addProduct(product, 244)

}

function processOrderData(records) {
    let hrefs = {}
    for (let i = 0; i < records.length; i++) {

        let href = records[i]['Источник товара']
        if (!hrefs[href]) hrefs[href] = {count:0,price:parseFloat(records[i]['Цена'])}
        hrefs[href].count += 1
    }
    return hrefs
}

async function order(href, data) {
    console.log(href)
    addMessage(`Order ${href}`, 241)
    let $ = null
    try {
        $ = await get("https://phoenix-plus.ru/catalog/"+href)
    } catch (e) {
        addMessage(e.message, 241)
        console.log(e.message)
        return
    }

    if ($==null) return

    if (!$('.product-info .catalog-quantity-line')[0]) {
        console.log("Out of stock")
        return
    }

    const price=Math.ceil(parseFloat($("span.product-price-current").text().replace(/[^.0-9]/g,"")))
    const pack=parseInt($('.product-info [data-pack]')[0].attribs["data-pack"])
    const stock=parseInt($('.product-info .catalog-quantity-line')[0].attribs["data-value"])
    const id=$('.product-quantity input')[0].attribs["data-id"]

    if (price>data.price*1.15) {
        console.log("Price too high")
        return
    }

    let quantity=data.count
    if (pack>1 && quantity%pack>0) {
        quantity=(Math.floor(quantity/pack)+1)*pack
    }

    let res=await post("https://phoenix-plus.ru/ajax/add2basket.php",{id,quantity,pack},true)
    console.log(res.text())
}


async function placeOrder(records) {
    let hrefs = processOrderData(records)

    await login()

    for (let href in hrefs) {
        await order(href, hrefs[href])
    }
    addMessage("Done", 241)
}


console.log('Start');


(async () => {
    //await parse()
    //const records = parseCsv(fs.readFileSync('C:\\Users\\<USER>\\downloads\\report_like_full_goods (35).csv').toString(), {columns: true, skip_empty_lines: true, relax: true,  bom:true, delimiter: ',' });

    await login()
    await order("tematicheskaya_produktsiya/bloki_doski_dlya_zapisey/magnitnyy_planing_210kh297_mm_ferroaglomerirovannyy_polimer_melovannaya_bumaga_s_glyantsevoy_laminats/\n", 1)

    //await placeOrder(records)

    console.log("Done")
})()

module.exports.parse = parse
module.exports.placeOrder = placeOrder