class CreateBrands < ActiveRecord::Migration[6.0]
  def change
    create_table :brands do |t|
      t.string :brand_name, unique: true
      t.boolean :skip, null: false, default: false

      t.timestamps
    end

    up_only do
      Product.select(:brand_name).distinct.pluck(:brand_name).each do |brand_name|
        Brand.create(brand_name: brand_name)
      end

      Purchase.all.each do |purchase|
        if purchase.skip_brands
          purchase.skip_brands.gsub(/[\r\n]/, ',').split(',').each do |brand_name|
            b = brand_name.strip
            if brand=Brand.where('LOWER(brand_name) = ?', b.downcase).first
              brand.update(skip: true)
            end
          end

        end
      end
    end
  end
end
