class AddTagsToCollections < ActiveRecord::Migration[5.2]
  def change
    @add_column = add_column :collections, :tags2, :json
    reversible do |dir|
      dir.up do
        Collection.all.each do |col|
          col_tags=ActiveRecord::Base.connection.execute("SELECT name FROM taggings,tags WHERE taggings.tag_id=tags.id AND taggable_id=#{col.id}").map {|l| l['name']}.uniq
          col.tags2=col_tags
          col.save
        end
      end
    end
    @add_column
  end
end
