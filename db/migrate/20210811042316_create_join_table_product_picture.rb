class CreateJoinTablerProductPicture < ActiveRecord::Migration[5.2]
  def change
    create_join_table :products, :pictures do |t|
      t.index [:product_id, :picture_id]
      t.index [:picture_id, :product_id]
    end
    reversible do |dir|
      dir.up do
        Picture.all.each do |p|
          next if p.product_id.nil?
          ActiveRecord::Base.connection.execute("insert into pictures_products (product_id,picture_id) values(#{p.product_id},#{p.id})")
        end
      end
    end
  end
end
